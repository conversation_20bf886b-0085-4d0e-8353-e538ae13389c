-- 用户常用菜单表
CREATE TABLE `sys_user_favorite_menu` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `menu_path` varchar(255) NOT NULL COMMENT '菜单路径',
  `menu_name` varchar(100) NOT NULL COMMENT '菜单名称',
  `menu_icon` varchar(100) DEFAULT NULL COMMENT '菜单图标',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序号',
  `system_id` int(11) DEFAULT NULL COMMENT '系统ID',
  `menu_id` int(11) DEFAULT NULL COMMENT '菜单ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` char(1) DEFAULT '1' COMMENT '状态 1:启用 0:禁用',
  `is_pinned` char(1) DEFAULT '0' COMMENT '是否PIN置顶 1:是 0:否',
  `pin_order` int(11) DEFAULT NULL COMMENT 'PIN排序号',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_menu` (`username`, `menu_path`),
  KEY `idx_username` (`username`),
  KEY `idx_menu_path` (`menu_path`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_pin_status` (`is_pinned`, `pin_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户常用菜单表';

-- 插入示例数据（可选）
INSERT INTO `sys_user_favorite_menu` (`username`, `menu_path`, `menu_name`, `menu_icon`, `sort_order`, `system_id`, `status`, `is_pinned`, `pin_order`) VALUES
('admin', '/oa/bpm/processInstance', 'OA审批', 'FlowerOutline', 1, 16, '1', '1', 1),
('admin', '/ams/property/amsProperty', '资产管理', 'CubeOutline', 2, 11, '1', '1', 2),
('admin', '/pms/pmsProject', '绩效管理', 'BarChartOutline', 3, 4, '1', '0', NULL),
('admin', '/hrm/emp/employeeInfo', '人员管理', 'PeopleOutline', 4, 2, '1', '0', NULL),
('admin', '/sys/sysConfig', '系统配置', 'SettingsOutline', 5, 1, '1', '0', NULL),
('admin', '/cms/contractManager/contractControl', '合同管理', 'DocumentTextOutline', 6, 5, '1', '0', NULL);
