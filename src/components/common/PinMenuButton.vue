<template>
  <n-button
    :type="isPinned ? 'warning' : 'default'"
    :loading="loading"
    size="small"
    @click="togglePin"
    class="pin-btn"
  >
    <template #icon>
      <n-icon>
        <StarOutline v-if="!isPinned" />
        <Star v-else />
      </n-icon>
    </template>
    {{ isPinned ? '取消PIN' : '设为PIN' }}
  </n-button>
</template>

<script setup lang="ts">
  import { ref, onMounted, computed } from 'vue'
  import { NButton, NIcon } from 'naive-ui'
  import { StarOutline, Star } from '@vicons/ionicons5'
  import { useUserFavoriteMenuStore } from '@/store/userFavoriteMenu'
  import { useRoute } from 'vue-router'
  import type { IUserFavoriteMenuDto } from '@/types/common/requestRes'

  // Props
  interface Props {
    menuPath?: string
    menuName?: string
    menuIcon?: string
    systemId?: number
    menuId?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    menuPath: '',
    menuName: '',
    menuIcon: '',
    systemId: 1,
    menuId: 0
  })

  // 状态管理
  const favoriteMenuStore = useUserFavoriteMenuStore()
  const route = useRoute()
  const loading = ref(false)
  const isPinned = ref(false)

  // 计算属性
  const currentMenuPath = computed(() => props.menuPath || route.path)
  const currentMenuName = computed(() => props.menuName || route.meta?.displayName || route.name || '未命名页面')

  /**
   * 检查当前菜单是否已PIN
   */
  const checkPinStatus = async () => {
    try {
      isPinned.value = favoriteMenuStore.isMenuPinned(currentMenuPath.value)
    } catch (error) {
      console.error('检查PIN状态失败:', error)
    }
  }

  /**
   * 切换PIN状态
   */
  const togglePin = async () => {
    try {
      loading.value = true
      
      // 检查PIN数量限制
      if (!isPinned.value && favoriteMenuStore.pinnedMenuCount >= 4) {
        window.$message?.warning('PIN功能最多只能设置4个')
        return
      }

      // 先确保菜单已收藏
      const isInFavorites = await favoriteMenuStore.isMenuFavorited(currentMenuPath.value)
      if (!isInFavorites) {
        // 先添加到收藏
        const menuData: IUserFavoriteMenuDto = {
          menuPath: currentMenuPath.value,
          menuName: currentMenuName.value,
          menuIcon: props.menuIcon,
          systemId: props.systemId,
          menuId: props.menuId
        }
        
        const addSuccess = await favoriteMenuStore.addToFavorites(menuData)
        if (!addSuccess) {
          window.$message?.error('请先添加到常用功能')
          return
        }
      }

      // 找到对应的收藏菜单
      const favoriteMenu = favoriteMenuStore.favoriteMenus.find(
        item => item.menuPath === currentMenuPath.value
      )

      if (favoriteMenu) {
        const success = await favoriteMenuStore.toggleMenuPin(favoriteMenu)
        if (success) {
          isPinned.value = !isPinned.value
        }
      }
    } catch (error) {
      console.error('切换PIN状态失败:', error)
      window.$message?.error('操作失败，请重试')
    } finally {
      loading.value = false
    }
  }

  // 生命周期
  onMounted(() => {
    checkPinStatus()
  })
</script>

<style scoped>
  .pin-btn {
    transition: all 0.3s ease;
  }

  .pin-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .pin-btn[data-type="warning"] {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    border-color: #f59e0b;
    color: white;
  }

  .pin-btn[data-type="warning"]:hover {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    border-color: #d97706;
  }
</style>
