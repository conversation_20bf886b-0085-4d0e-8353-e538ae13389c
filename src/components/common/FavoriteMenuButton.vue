<template>
  <n-button
    :type="isFavorited ? 'warning' : 'default'"
    :loading="loading"
    size="small"
    @click="toggleFavorite"
    class="favorite-btn"
  >
    <template #icon>
      <n-icon>
        <StarOutline v-if="!isFavorited" />
        <Star v-else />
      </n-icon>
    </template>
    {{ isFavorited ? '取消收藏' : '添加收藏' }}
  </n-button>
</template>

<script setup lang="ts">
  import { ref, onMounted, computed } from 'vue'
  import { NButton, NIcon } from 'naive-ui'
  import { StarOutline, Star } from '@vicons/ionicons5'
  import { useUserFavoriteMenuStore } from '@/store/userFavoriteMenu'
  import { useRoute } from 'vue-router'
  import type { IUserFavoriteMenuDto } from '@/types/common/requestRes'

  // Props
  interface Props {
    menuPath?: string
    menuName?: string
    menuIcon?: string
    systemId?: number
    menuId?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    menuPath: '',
    menuName: '',
    menuIcon: '',
    systemId: 1,
    menuId: 0
  })

  // 状态管理
  const favoriteMenuStore = useUserFavoriteMenuStore()
  const route = useRoute()
  const loading = ref(false)
  const isFavorited = ref(false)

  // 计算属性
  const currentMenuPath = computed(() => props.menuPath || route.path)
  const currentMenuName = computed(() => props.menuName || route.meta?.displayName || route.name || '未命名页面')

  /**
   * 检查当前菜单是否已收藏
   */
  const checkFavoriteStatus = async () => {
    try {
      isFavorited.value = await favoriteMenuStore.isMenuFavorited(currentMenuPath.value)
    } catch (error) {
      console.error('检查收藏状态失败:', error)
    }
  }

  /**
   * 切换收藏状态
   */
  const toggleFavorite = async () => {
    try {
      loading.value = true
      
      const menuData: IUserFavoriteMenuDto = {
        menuPath: currentMenuPath.value,
        menuName: currentMenuName.value,
        menuIcon: props.menuIcon,
        systemId: props.systemId,
        menuId: props.menuId
      }

      const success = await favoriteMenuStore.toggleFavorite(menuData)
      
      if (success) {
        // 更新本地状态
        isFavorited.value = !isFavorited.value
      }
    } catch (error) {
      console.error('切换收藏状态失败:', error)
      window.$message?.error('操作失败，请重试')
    } finally {
      loading.value = false
    }
  }

  // 生命周期
  onMounted(() => {
    checkFavoriteStatus()
  })
</script>

<style scoped>
  .favorite-btn {
    transition: all 0.3s ease;
  }

  .favorite-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
</style>
