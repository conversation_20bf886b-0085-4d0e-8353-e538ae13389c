import request from '@/utils/request'
import { RequestType } from '@/types/enums/enums'
import type {
  IUserFavoriteMenuDto,
  IUserFavoriteMenuVo,
  IPinToggleParams,
  IPinOrderUpdateParams,
  IMenuFavoriteCheckParams,
  IUserFavoriteMenuListPromise,
  IUserPinnedMenuListPromise,
  IMenuFavoriteCheckPromise,
  IOperationPromise
} from '@/types/common/requestRes'

/**
 * 用户常用菜单接口类型定义
 * @deprecated 请使用 IUserFavoriteMenuVo 替代
 */
export interface UserFavoriteMenu {
  id?: number
  username?: string
  menuPath: string
  menuName: string
  menuIcon?: string
  sortOrder?: number
  systemId?: number
  menuId?: number
  createTime?: string
  updateTime?: string
  status?: string
  isPinned?: string
  pinOrder?: number
  remark?: string
  warnNum?: number
}

/**
 * 获取当前用户的常用菜单
 */
export function getCurrentUserFavoriteMenus(): IUserFavoriteMenuListPromise {
  return request({
    url: 'core/sysUserFavoriteMenu/myFavorites',
    method: RequestType.POST,
    hideLoadingBar: true,
  })
}

/**
 * 查询用户常用菜单列表
 * @param param 查询参数
 */
export function queryUserFavoriteMenus(param: IUserFavoriteMenuDto): IUserFavoriteMenuListPromise {
  return request({
    url: 'core/sysUserFavoriteMenu/list',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 添加常用菜单
 * @param param 菜单信息
 */
export function addFavoriteMenu(param: IUserFavoriteMenuDto): IOperationPromise {
  return request({
    url: 'core/sysUserFavoriteMenu/add',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 删除常用菜单
 * @param param 菜单信息
 */
export function removeFavoriteMenu(param: { id?: number; menuPath?: string }): IOperationPromise {
  return request({
    url: 'core/sysUserFavoriteMenu/delete',
    method: RequestType.DEL,
    data: param,
  })
}

/**
 * 批量更新排序
 * @param param 排序信息
 */
export function updateFavoriteMenuSort(param: { id: number; sortOrder: number }): IOperationPromise {
  return request({
    url: 'core/sysUserFavoriteMenu/updateSort',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 检查菜单是否已收藏
 * @param menuPath 菜单路径
 */
export function checkMenuFavorited(menuPath: string): IMenuFavoriteCheckPromise {
  return request({
    url: 'core/sysUserFavoriteMenu/checkFavorite',
    method: RequestType.POST,
    data: { menuPath },
    hideLoadingBar: true,
  })
}

/**
 * 批量保存常用菜单
 * @param menuList 菜单列表
 */
export function batchSaveFavoriteMenus(menuList: IUserFavoriteMenuDto[]): IOperationPromise {
  return request({
    url: 'core/sysUserFavoriteMenu/batchSave',
    method: RequestType.POST,
    data: { menuList },
  })
}

/**
 * 获取当前用户的PIN菜单
 */
export function getCurrentUserPinnedMenus(): IUserPinnedMenuListPromise {
  return request({
    url: 'core/sysUserFavoriteMenu/myPins',
    method: RequestType.POST,
    hideLoadingBar: true,
  })
}

/**
 * 设置/取消PIN
 * @param param PIN信息
 */
export function togglePin(param: IPinToggleParams): IOperationPromise {
  return request({
    url: 'core/sysUserFavoriteMenu/togglePin',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 批量更新PIN排序
 * @param pinList PIN列表
 */
export function updatePinOrder(pinList: IUserFavoriteMenuDto[]): IOperationPromise {
  return request({
    url: 'core/sysUserFavoriteMenu/updatePinOrder',
    method: RequestType.POST,
    data: { menuList: pinList },
  })
}

/**
 * 检查菜单是否已PIN
 * @param menuPath 菜单路径
 */
export function checkMenuPinned(menuPath: string): IMenuFavoriteCheckPromise {
  return request({
    url: 'core/sysUserFavoriteMenu/checkPinned',
    method: RequestType.POST,
    data: { menuPath },
    hideLoadingBar: true,
  })
}
