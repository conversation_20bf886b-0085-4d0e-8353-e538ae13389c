class Res<T> {
  code: number = -1
  message: string = ''
}

// 通用返回值
export class IRes<T = any> extends Res<T> {
  data: T

  constructor(data: T) {
    super()
    this.data = data
  }
}

// 通用分页返回
export class IPageRes<T = any> extends Res<T> {
  data: IPage = new IPage()
}

// 分页返回
class IPage {
  countId: number = -1
  current: number = -1
  hitCount: boolean = false
  maxLimit: number = -1
  optimizeCountSql: boolean = true
  orders: any[] = []
  pages: number = -1
  records: any[] = []
  searchCount: Boolean = true
  size: number = -1
  total: number = 0
}

/**
 * 封装 Promise IPageRes
 */
export class IPageResPromise<T = any[]> extends Promise<IPageRes<T>> {
  constructor(executor: (resolve: (value: IPageRes<T>) => void, reject: (reason?: any) => void) => void) {
    super(executor)
  }
}

/**
 * 封装 Promise IRes
 */
export class IResPromise<T = any> extends Promise<IRes<T>> {
  constructor(executor: (resolve: (value: IRes<T>) => void, reject: (reason?: any) => void) => void) {
    super(executor)
  }
}
