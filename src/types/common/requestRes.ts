class Res<T> {
  code: number = -1
  message: string = ''
}

// 通用返回值
export class IRes<T = any> extends Res<T> {
  data: T
  
  constructor(data: T) {
    super()
    this.data = data
  }
}

// 通用分页返回
export class IPageRes<T = any> extends Res<T> {
  data: IPage = new IPage()
}

// 分页返回
class IPage {
  countId: number = -1
  current: number = -1
  hitCount: boolean = false
  maxLimit: number = -1
  optimizeCountSql: boolean = true
  orders: any[] = []
  pages: number = -1
  records: any[] = []
  searchCount: Boolean = true
  size: number = -1
  total: number = 0
}

/**
 * 封装 Promise IPageRes
 */
export class IPageResPromise<T = any[]> extends Promise<IPageRes<T>> {
  constructor(executor: (resolve: (value: IPageRes<T>) => void, reject: (reason?: any) => void) => void) {
    super(executor)
  }
}

/**
 * 封装 Promise IRes
 */
export class IResPromise<T = any> extends Promise<IRes<T>> {
  constructor(executor: (resolve: (value: IRes<T>) => void, reject: (reason?: any) => void) => void) {
    super(executor)
  }
}

// ==================== 用户常用功能相关类型定义 ====================

/**
 * 用户常用菜单基础接口
 */
export interface IUserFavoriteMenuBase {
  /** 主键ID */
  id?: number
  /** 用户名 */
  username?: string
  /** 菜单路径 */
  menuPath: string
  /** 菜单名称 */
  menuName: string
  /** 菜单图标 */
  menuIcon?: string
  /** 排序号 */
  sortOrder?: number
  /** 系统ID */
  systemId?: number
  /** 菜单ID */
  menuId?: number
  /** 创建时间 */
  createTime?: string
  /** 更新时间 */
  updateTime?: string
  /** 状态 1:启用 0:禁用 */
  status?: string
  /** 是否PIN置顶 1:是 0:否 */
  isPinned?: string
  /** PIN排序号 */
  pinOrder?: number
  /** 备注 */
  remark?: string
}

/**
 * 用户常用菜单数据传输对象
 */
export interface IUserFavoriteMenuDto extends IUserFavoriteMenuBase {
  /** 批量操作时的菜单列表 */
  menuList?: IUserFavoriteMenuDto[]
}

/**
 * 用户常用菜单视图对象
 */
export interface IUserFavoriteMenuVo extends IUserFavoriteMenuBase {
  /** 警告数量 - 从快速到达功能获取 */
  warnNum?: number
}

/**
 * PIN功能操作参数
 */
export interface IPinToggleParams {
  /** 菜单ID */
  id?: number
  /** 菜单路径 */
  menuPath?: string
  /** PIN状态 */
  isPinned: string
  /** PIN排序号 */
  pinOrder?: number
}

/**
 * PIN排序更新参数
 */
export interface IPinOrderUpdateParams {
  /** PIN菜单列表 */
  menuList: IUserFavoriteMenuDto[]
}

/**
 * 菜单收藏状态检查参数
 */
export interface IMenuFavoriteCheckParams {
  /** 菜单路径 */
  menuPath: string
}

// ==================== API响应类型定义 ====================

/**
 * 获取用户常用菜单列表响应
 */
export type IUserFavoriteMenuListRes = IRes<IUserFavoriteMenuVo[]>

/**
 * 获取用户PIN菜单列表响应
 */
export type IUserPinnedMenuListRes = IRes<IUserFavoriteMenuVo[]>

/**
 * 菜单收藏状态检查响应
 */
export type IMenuFavoriteCheckRes = IRes<boolean>

/**
 * 通用操作成功响应
 */
export type IOperationSuccessRes = IRes<null>

// ==================== Promise类型定义 ====================

/**
 * 用户常用菜单列表Promise
 */
export type IUserFavoriteMenuListPromise = Promise<IUserFavoriteMenuListRes>

/**
 * 用户PIN菜单列表Promise
 */
export type IUserPinnedMenuListPromise = Promise<IUserPinnedMenuListRes>

/**
 * 菜单收藏状态检查Promise
 */
export type IMenuFavoriteCheckPromise = Promise<IMenuFavoriteCheckRes>

/**
 * 通用操作Promise
 */
export type IOperationPromise = Promise<IOperationSuccessRes>
