/**
 * 用户常用功能模块类型定义
 * 
 * 这个文件重新导出了用户常用功能相关的类型定义，
 * 方便在组件中使用，避免从通用类型文件中导入过多类型
 */

// 重新导出核心类型
export type {
  IUserFavoriteMenuBase,
  IUserFavoriteMenuDto,
  IUserFavoriteMenuVo,
  IPinToggleParams,
  IPinOrderUpdateParams,
  IMenuFavoriteCheckParams,
  IUserFavoriteMenuListRes,
  IUserPinnedMenuListRes,
  IMenuFavoriteCheckRes,
  IOperationSuccessRes,
  IUserFavoriteMenuListPromise,
  IUserPinnedMenuListPromise,
  IMenuFavoriteCheckPromise,
  IOperationPromise
} from '@/types/common/requestRes'

/**
 * 菜单项基础信息（用于移动端显示）
 */
export interface IMenuItemDisplay {
  /** 菜单路径 */
  path: string
  /** 菜单名称 */
  name: string
  /** 菜单元信息 */
  meta: {
    /** 显示名称 */
    displayName: string
    /** 图标名称 */
    icon?: string
  }
  /** 警告数量 */
  warnNum?: number
}

/**
 * PIN功能配置
 */
export interface IPinConfig {
  /** 最大PIN数量 */
  maxCount: number
  /** 是否自动排序 */
  autoSort: boolean
  /** 移动端显示 */
  showInMobile: boolean
  /** 桌面端显示 */
  showInDesktop: boolean
  /** 渐变色配置 */
  gradientColors: {
    /** 起始色 */
    from: string
    /** 结束色 */
    to: string
  }
}

/**
 * 用户常用功能统计信息
 */
export interface IUserFavoriteStats {
  /** 常用功能数量 */
  favoriteCount: number
  /** PIN功能数量 */
  pinnedCount: number
  /** 今日使用次数 */
  todayUsageCount: number
  /** 最近更新时间 */
  lastUpdateTime: string
}

/**
 * 菜单操作结果
 */
export interface IMenuOperationResult {
  /** 操作是否成功 */
  success: boolean
  /** 操作消息 */
  message?: string
  /** 操作数据 */
  data?: any
}

/**
 * 菜单排序项
 */
export interface IMenuSortItem {
  /** 菜单ID */
  id: number
  /** 排序号 */
  sortOrder: number
  /** PIN排序号 */
  pinOrder?: number
}

/**
 * 批量操作参数
 */
export interface IBatchOperationParams {
  /** 操作类型 */
  operation: 'add' | 'remove' | 'sort' | 'pin' | 'unpin'
  /** 菜单列表 */
  menuList: IUserFavoriteMenuDto[]
}

/**
 * 菜单搜索参数
 */
export interface IMenuSearchParams {
  /** 搜索关键词 */
  keyword?: string
  /** 系统ID */
  systemId?: number
  /** 是否只显示PIN */
  pinnedOnly?: boolean
  /** 是否只显示收藏 */
  favoritedOnly?: boolean
}

/**
 * 菜单使用统计
 */
export interface IMenuUsageStats {
  /** 菜单路径 */
  menuPath: string
  /** 菜单名称 */
  menuName: string
  /** 使用次数 */
  usageCount: number
  /** 最后使用时间 */
  lastUsedTime: string
  /** 是否PIN */
  isPinned: boolean
  /** 是否收藏 */
  isFavorited: boolean
}

/**
 * 默认PIN配置
 */
export const DEFAULT_PIN_CONFIG: IPinConfig = {
  maxCount: 4,
  autoSort: true,
  showInMobile: true,
  showInDesktop: false,
  gradientColors: {
    from: '#fef3c7',
    to: '#fed7aa'
  }
}

/**
 * 菜单状态枚举
 */
export enum MenuStatus {
  /** 启用 */
  ENABLED = '1',
  /** 禁用 */
  DISABLED = '0'
}

/**
 * PIN状态枚举
 */
export enum PinStatus {
  /** 已PIN */
  PINNED = '1',
  /** 未PIN */
  UNPINNED = '0'
}

/**
 * 操作类型枚举
 */
export enum OperationType {
  /** 添加收藏 */
  ADD_FAVORITE = 'add_favorite',
  /** 移除收藏 */
  REMOVE_FAVORITE = 'remove_favorite',
  /** 设置PIN */
  SET_PIN = 'set_pin',
  /** 取消PIN */
  UNSET_PIN = 'unset_pin',
  /** 更新排序 */
  UPDATE_SORT = 'update_sort',
  /** 批量操作 */
  BATCH_OPERATION = 'batch_operation'
}

/**
 * 类型守卫：检查是否为有效的用户常用菜单
 */
export function isValidUserFavoriteMenu(menu: any): menu is IUserFavoriteMenuVo {
  return menu && 
         typeof menu.menuPath === 'string' && 
         typeof menu.menuName === 'string' &&
         menu.menuPath.length > 0 &&
         menu.menuName.length > 0
}

/**
 * 类型守卫：检查是否为PIN菜单
 */
export function isPinnedMenu(menu: IUserFavoriteMenuVo): boolean {
  return menu.isPinned === PinStatus.PINNED
}

/**
 * 类型守卫：检查是否为启用状态的菜单
 */
export function isEnabledMenu(menu: IUserFavoriteMenuVo): boolean {
  return menu.status === MenuStatus.ENABLED
}
