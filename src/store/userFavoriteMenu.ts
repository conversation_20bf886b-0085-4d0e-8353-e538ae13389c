import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import {
  getCurrentUserFavoriteMenus,
  getCurrentUserPinnedMenus,
  addFavoriteMenu,
  removeFavoriteMenu,
  updateFavoriteMenuSort,
  checkMenuFavorited,
  togglePin,
  updatePinOrder,
  type UserFavoriteMenu
} from '@/api/sys/userFavoriteMenu'

/**
 * 用户常用菜单状态管理
 */
export const useUserFavoriteMenuStore = defineStore('UserFavoriteMenu', () => {
  // 状态
  const favoriteMenus = ref<UserFavoriteMenu[]>([])
  const pinnedMenus = ref<UserFavoriteMenu[]>([])
  const loading = ref(false)
  const initialized = ref(false)

  // 计算属性
  const favoriteMenuCount = computed(() => favoriteMenus.value.length)
  const pinnedMenuCount = computed(() => pinnedMenus.value.length)

  // 获取前N个常用菜单（用于移动端快捷功能显示）
  const getTopFavoriteMenus = computed(() => (count: number = 6) => {
    return favoriteMenus.value.slice(0, count)
  })

  // 获取前N个PIN菜单（用于移动端PIN区域显示）
  const getTopPinnedMenus = computed(() => (count: number = 4) => {
    return pinnedMenus.value.slice(0, count)
  })

  /**
   * 获取用户常用菜单列表
   */
  const fetchFavoriteMenus = async () => {
    try {
      loading.value = true
      const res = await getCurrentUserFavoriteMenus()

      if (res.success && res.data) {
        favoriteMenus.value = res.data
        initialized.value = true
        console.log('✅ 用户常用菜单加载成功:', favoriteMenus.value.length, '个')
      } else {
        console.warn('⚠️ 获取用户常用菜单失败:', res.message)
        favoriteMenus.value = []
      }
    } catch (error) {
      console.error('❌ 获取用户常用菜单异常:', error)
      favoriteMenus.value = []
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取用户PIN菜单列表
   */
  const fetchPinnedMenus = async () => {
    try {
      const res = await getCurrentUserPinnedMenus()

      if (res.success && res.data) {
        pinnedMenus.value = res.data
        console.log('✅ 用户PIN菜单加载成功:', pinnedMenus.value.length, '个')
      } else {
        console.warn('⚠️ 获取用户PIN菜单失败:', res.message)
        pinnedMenus.value = []
      }
    } catch (error) {
      console.error('❌ 获取用户PIN菜单异常:', error)
      pinnedMenus.value = []
    }
  }

  /**
   * 添加常用菜单
   */
  const addToFavorites = async (menu: UserFavoriteMenu) => {
    try {
      // 检查是否已存在
      const exists = favoriteMenus.value.some(item => item.menuPath === menu.menuPath)
      if (exists) {
        window.$message?.warning('该菜单已在常用功能中')
        return false
      }

      // 设置排序号
      if (!menu.sortOrder) {
        const maxSort = Math.max(...favoriteMenus.value.map(item => item.sortOrder || 0), 0)
        menu.sortOrder = maxSort + 1
      }

      const res = await addFavoriteMenu(menu)
      if (res.success) {
        // 重新获取列表
        await fetchFavoriteMenus()
        window.$message?.success('添加常用功能成功')
        return true
      } else {
        window.$message?.error(res.message || '添加常用功能失败')
        return false
      }
    } catch (error) {
      console.error('❌ 添加常用菜单异常:', error)
      window.$message?.error('添加常用功能失败')
      return false
    }
  }

  /**
   * 删除常用菜单
   */
  const removeFromFavorites = async (menuPath: string) => {
    try {
      const res = await removeFavoriteMenu({ menuPath })
      if (res.success) {
        // 从本地列表中移除
        favoriteMenus.value = favoriteMenus.value.filter(item => item.menuPath !== menuPath)
        window.$message?.success('移除常用功能成功')
        return true
      } else {
        window.$message?.error(res.message || '移除常用功能失败')
        return false
      }
    } catch (error) {
      console.error('❌ 删除常用菜单异常:', error)
      window.$message?.error('移除常用功能失败')
      return false
    }
  }

  /**
   * 更新菜单排序
   */
  const updateMenuSort = async (id: number, sortOrder: number) => {
    try {
      const res = await updateFavoriteMenuSort({ id, sortOrder })
      if (res.success) {
        // 更新本地数据
        const menu = favoriteMenus.value.find(item => item.id === id)
        if (menu) {
          menu.sortOrder = sortOrder
        }
        // 重新排序
        favoriteMenus.value.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
        return true
      }
      return false
    } catch (error) {
      console.error('❌ 更新菜单排序异常:', error)
      return false
    }
  }

  /**
   * 检查菜单是否已收藏
   */
  const isMenuFavorited = async (menuPath: string): Promise<boolean> => {
    try {
      // 先检查本地缓存
      const localExists = favoriteMenus.value.some(item => item.menuPath === menuPath)
      if (localExists) return true

      // 如果本地没有，且还未初始化，则调用API检查
      if (!initialized.value) {
        const res = await checkMenuFavorited(menuPath)
        return res.success && res.data
      }

      return false
    } catch (error) {
      console.error('❌ 检查菜单收藏状态异常:', error)
      return false
    }
  }

  /**
   * 切换菜单收藏状态
   */
  const toggleFavorite = async (menu: UserFavoriteMenu): Promise<boolean> => {
    const isFavorited = await isMenuFavorited(menu.menuPath)
    
    if (isFavorited) {
      return await removeFromFavorites(menu.menuPath)
    } else {
      return await addToFavorites(menu)
    }
  }

  /**
   * 设置/取消PIN
   */
  const toggleMenuPin = async (menu: UserFavoriteMenu): Promise<boolean> => {
    try {
      // 检查PIN数量限制（最多4个）
      if (!menu.isPinned || menu.isPinned === '0') {
        if (pinnedMenus.value.length >= 4) {
          window.$message?.warning('PIN功能最多只能设置4个')
          return false
        }
      }

      const isPinned = menu.isPinned === '1' ? '0' : '1'
      const pinOrder = isPinned === '1' ? pinnedMenus.value.length + 1 : undefined

      const res = await togglePin({
        id: menu.id,
        menuPath: menu.menuPath,
        isPinned,
        pinOrder
      })

      if (res.success) {
        // 重新获取数据
        await Promise.all([fetchFavoriteMenus(), fetchPinnedMenus()])
        window.$message?.success(isPinned === '1' ? 'PIN设置成功' : '取消PIN成功')
        return true
      } else {
        window.$message?.error(res.message || 'PIN操作失败')
        return false
      }
    } catch (error) {
      console.error('❌ PIN操作异常:', error)
      window.$message?.error('PIN操作失败')
      return false
    }
  }

  /**
   * 更新PIN排序
   */
  const updatePinMenuOrder = async (pinList: UserFavoriteMenu[]): Promise<boolean> => {
    try {
      const res = await updatePinOrder(pinList)
      if (res.success) {
        // 更新本地数据
        pinnedMenus.value = [...pinList].sort((a, b) => (a.pinOrder || 0) - (b.pinOrder || 0))
        return true
      }
      return false
    } catch (error) {
      console.error('❌ 更新PIN排序异常:', error)
      return false
    }
  }

  /**
   * 检查菜单是否已PIN
   */
  const isMenuPinned = (menuPath: string): boolean => {
    return pinnedMenus.value.some(item => item.menuPath === menuPath)
  }

  /**
   * 重置状态
   */
  const reset = () => {
    favoriteMenus.value = []
    pinnedMenus.value = []
    loading.value = false
    initialized.value = false
  }

  return {
    // 状态
    favoriteMenus,
    pinnedMenus,
    loading,
    initialized,

    // 计算属性
    favoriteMenuCount,
    pinnedMenuCount,
    getTopFavoriteMenus,
    getTopPinnedMenus,

    // 方法
    fetchFavoriteMenus,
    fetchPinnedMenus,
    addToFavorites,
    removeFromFavorites,
    updateMenuSort,
    isMenuFavorited,
    toggleFavorite,
    toggleMenuPin,
    updatePinMenuOrder,
    isMenuPinned,
    reset,
  }
})
