package com.jp.med.core.modules.oaTask.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.core.modules.oaTask.dto.FastArriveDto;
import com.jp.med.core.modules.oaTask.vo.FastArriveVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * OA系统--跳转系统路由信息
 * <AUTHOR>
 * @email -
 * @date 2024-05-28 14:54:42
 */
@Mapper
public interface FastArriveReadMapper extends BaseMapper<FastArriveDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<FastArriveVo> queryList(FastArriveDto dto);

    /**
     * 查询用户oa快捷导航
     * @param dto
     * @return
     */
    List<FastArriveVo> oaTaskLst(FastArriveDto dto);
}
