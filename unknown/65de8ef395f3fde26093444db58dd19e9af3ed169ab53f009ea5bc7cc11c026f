package com.jp.med.core.modules.sys.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.core.modules.sys.mapper.read.SysConfigReadMapper;
import com.jp.med.core.modules.sys.dto.SysConfigDto;
import com.jp.med.common.entity.sys.SysConfigEntity;
import com.jp.med.core.modules.sys.service.read.SysConfigReadService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional(readOnly = true)
@Service
public class SysConfigReadServiceImpl extends ServiceImpl<SysConfigReadMapper, SysConfigDto> implements SysConfigReadService {
    @Autowired
    private SysConfigReadMapper sysConfigReadMapper;

    @Override
    public List<SysConfigEntity> queryList(SysConfigDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return sysConfigReadMapper.queryList(dto);
    }

}
