package com.jp.med.core.modules.sys.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import com.jp.med.common.dto.emp.HrmOrgDto;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 组织架构
 *
 * <AUTHOR>
 * @email -
 * @date 2023-03-16 17:25:48
 */
@Data
@TableName("sys_org")
public class SysOrgDto extends CommonQueryDto {
    /**
     * id 集合
     */
    @TableField(exist = false)
    private String[] ids;
    /**
     * 组织架构ID
     */
    @TableId("org_id")
    @Excel(name = "科室编码")
    private String orgId;

    /**
     * 组织架构ID( 新增和修改时候用的 )
     */
    @TableField(exist = false)
    private String organizationId;
    /**
     * 父组织架构ID
     */
    @TableField("org_parent_id")
    @Excel(name = "上级科室编码")
    private String orgParentId;
    /**
     * 组织架构名称
     */
    @TableField("org_name")
    @Excel(name = "科室名称")
    private String orgName;
    /**
     * 医疗机构编号
     */
    @TableField("hospital_id")
    @Excel(name = "医疗机构编号")
    private String hospitalId;
    /**
     * 查询组织名称
     */
    @TableField(exist = false)
    private String queryOrgName;
    /**
     * 上传的文件
     */
    @TableField(exist = false)
    private MultipartFile file;

    /** 批量删除时指定，指定后以当前数据批量操作 */
    @TableField(exist = false)
    List<SysOrgDto> deleteList;

    /** 批量新增时指定，指定后以当前数据批量操作 */
    @TableField(exist = false)
    List<SysOrgDto> addList;

    /** hrm 组织架构的集合 */
    @TableField(exist = false)
    List<HrmOrgDto> hrmOrgDtos;

    /** 同步 hrm 组织架构信息 */
    @TableField(exist = false)
    private String[] orgInfo;

}
