package com.jp.med.core.modules.itf.controller;

import com.jp.med.common.dto.itf.CoreSysItfMgtDto;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.common.util.ULIDUtil;
import com.jp.med.core.config.GlobalInitConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.core.modules.itf.service.read.CoreSysItfMgtReadService;
import com.jp.med.core.modules.itf.service.write.CoreSysItfMgtWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 系统接口管理
 * <AUTHOR>
 * @email -
 * @date 2023-12-25 11:45:08
 */
@Api(value = "系统接口管理", tags = "系统接口管理")
@RestController
@RequestMapping("coreSysItfMgt")
public class CoreSysItfMgtController {

    @Autowired
    private CoreSysItfMgtReadService coreSysItfMgtReadService;

    @Autowired
    private CoreSysItfMgtWriteService coreSysItfMgtWriteService;

    @Autowired
    private GlobalInitConfig globalInitConfig;

    /**
     * 列表
     */
    @ApiOperation("查询系统接口管理")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody CoreSysItfMgtDto dto){
        return CommonResult.paging(coreSysItfMgtReadService.queryList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("刷新缓存")
    @PostMapping("/refreshCache")
    public CommonResult<?> refreshCache(@RequestBody CoreSysItfMgtDto dto){
        globalInitConfig.itfMgtConfig("刷新接口配置");
        return CommonResult.success();
    }

    @ApiOperation("查询接口管理数据")
    @PostMapping("/queryList")
    public CommonFeignResult queryList(@RequestBody CoreSysItfMgtDto dto){
        return CommonFeignResult.build().put(CommonFeignResult.DATA_KEY, coreSysItfMgtReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增系统接口管理")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody CoreSysItfMgtDto dto){
        dto.setAuthCode(ULIDUtil.generate());
        coreSysItfMgtWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改系统接口管理")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody CoreSysItfMgtDto dto){
        coreSysItfMgtWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除系统接口管理")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody CoreSysItfMgtDto dto){
        coreSysItfMgtWriteService.removeById(dto);
        return CommonResult.success();
    }

}
