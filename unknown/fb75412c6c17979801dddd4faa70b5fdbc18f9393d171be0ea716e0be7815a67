package com.jp.med.core.modules.common.mapper.write;

import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.entity.audit.AuditRes;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/11 14:58
 * @description:
 */
public interface CommonAuditWriteMapper {

    /**
     * 新增审核详情
     */
    void saveAuditDetail(AuditDetail auditDetail);


    /**
     * 更新审核详情
     * @param dto
     */
    void updateAuditDetail(AuditDetail dto);

    /**
     * 更新消息ID
     * @param dto
     */
    void updateMessageId(AuditDetail dto);

    /**
     * 更新当前批次号的上级批次号
     *
     * @param batchNum        上级批次号
     * @param batchNumList    子集批次号
     * @param recordTableName 记录表名
     */
    void updateParentBecho(@Param("parentBecho") String batchNum,
                           @Param("children") List<String> batchNumList,
                           @Param("recordTableName") String recordTableName);

    /**
     * 设置审核流程的审核人信息
     * @param dto
     */
    void setAuditorInProcess(AuditDetail dto);

    /**
     * 添加审核结果
     * @param auditRes
     */
    void saveAuditRes(AuditRes auditRes);

    /**
     * 更新审核结果
     * @param auditRes
     */
    void updateAuditRes(AuditRes auditRes);
}
