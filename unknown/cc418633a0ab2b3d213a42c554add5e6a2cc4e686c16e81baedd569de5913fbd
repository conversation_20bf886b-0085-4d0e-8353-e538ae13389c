package com.jp.med.core.modules.sys.mapper.read;

import com.jp.med.common.dto.prcs.PrcsDto;
import com.jp.med.common.vo.PrcsVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 流程主表
 * <AUTHOR>
 * @email -
 * @date 2023-12-13 16:02:39
 */
@Mapper
public interface SysPrcsReadMapper extends BaseMapper<PrcsDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<PrcsVo> queryList(PrcsDto dto);
}
