package com.jp.med.core.modules.common.service.write;

import com.jp.med.common.dto.app.AppMsgSup;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.entity.payload.AuditPayload;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/11 14:55
 * @description:
 */
public interface CommonAuditWriteService {

    /**
     * 写入审核表
     *
     * @param dto         参数
     */
    void saveAuditDetail(AuditDetail dto);

    /**
     * 不推送消息的保存审核
     *
     * @param dto     参数
     * @param details 详情
     * @param empCode 创建人
     */
    void notPushSaveAuditDetail(AuditDetail dto, List<AuditDetail> details, String empCode);

    /**
     * 修改审核详情
     * @param dto
     */
    void updateAuditDetail(AuditDetail dto);


    /**
     * 推送消息
     * @param details 审核详情
     * @param appMsgSup 消息辅助信息
     * @param auditPayload 审核数据
     */
    void pushMessage(List<AuditDetail> details, AppMsgSup appMsgSup, AuditPayload auditPayload, AuditDetail dto);

    /**
     * 批量修改审核详情
     * @param dto
     */
    void batchUpdateAuditDetail(AuditDetail dto);

    /**
     * 更改上级审核批次号
     * @param dto
     */
    void updateParentBecho(AuditDetail dto);


    /**
     * 设置审核流程的审核人信息
     * @param dto
     */
    void setAuditorInProcess(AuditDetail dto);

    void batchUpdateAuditDetailMulti(AuditDetail dto);
}
