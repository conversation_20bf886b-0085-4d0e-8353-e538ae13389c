package com.jp.med.core.modules.user.mapper.read;

import com.jp.med.common.dto.user.UserSignDto;
import com.jp.med.common.vo.UserSignVo;
import com.jp.med.core.modules.user.dto.CoreSysUserSignDto;
import com.jp.med.core.modules.user.vo.CoreSysUserSignVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户签章
 *
 * <AUTHOR>
 * @email -
 * @date 2024-01-16 20:01:51
 */
@Mapper
public interface CoreSysUserSignReadMapper extends BaseMapper<CoreSysUserSignDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<CoreSysUserSignVo> queryList(CoreSysUserSignDto dto);


    UserSignVo querySign(UserSignDto dto);
}
