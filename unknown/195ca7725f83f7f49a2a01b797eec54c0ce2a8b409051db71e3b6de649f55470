package com.jp.med.core.modules.sys.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 系统配置信息表
 *
 * <AUTHOR>
 * @email -
 * @date 2023-03-24 09:31:58
 */
@Data
@TableName("sys_config" )
public class SysConfigDto extends CommonQueryDto {

    /**  */
    @TableId("id")
    private Long id;

    /** key */
    @TableField("param_key")
    private String paramKey;

    /** value */
    @TableField("param_value")
    private String paramValue;

    /** 状态   0：隐藏   1：显示 */
    @TableField("status")
    private String status;

    /** 备注 */
    @TableField("remark")
    private String remark;

}
