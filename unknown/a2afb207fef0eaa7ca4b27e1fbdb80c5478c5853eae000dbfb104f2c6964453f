package com.jp.med.core.modules.oaTask.vo;

import lombok.Data;

/**
 * OA系统--跳转系统路由信息
 * <AUTHOR>
 * @email -
 * @date 2024-05-28 14:54:42
 */
@Data
public class FastArriveVo {


    private Integer id;

	/** 部门Id */
	private String orgId;

	/** 部门Id */
	private String orgName;

	/** 跳转系统URL */
	private Integer sysId;

	/** 跳转系统URL */
	private String sysName;

	/** 跳转路由path */
	private Integer menuId;

	/** 跳转路由path */
	private String menuName;

	/** 跳转路由path */
	private String path;

	/** 标题 */
	private String title;

	/** 描述 */
	private String dscr;

	/** 查询提示数量url */
	private String queryNumUrl;

	/** 排序 */
	private Integer sort;

	/** svg图标 */
	private String svgIcon;

	/** svg颜色 */
	private String svgColor;

	/** josn */
	private String dtoJson;

}
