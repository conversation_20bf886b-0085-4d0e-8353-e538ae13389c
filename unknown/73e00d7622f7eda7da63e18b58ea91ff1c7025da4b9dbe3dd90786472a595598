package com.jp.med.core.modules.user.feign.gateway;

import com.jp.med.common.dto.common.CommonFeignDto;
import com.jp.med.common.entity.common.CommonFeignResult;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@RefreshScope
@FeignClient(name = "med-gateway", url = "${custom.gateway.med-gateway-service-uri}")
public interface EncryFeignService {

    /**
     * 加密
     * @return
     */
    @RequestMapping(value = "/password/encry")
    CommonFeignResult encryPwd(@RequestParam("password") String password);


    /** 验证密码 */
    @RequestMapping(value = "/password/match")
    CommonFeignResult match(@RequestBody CommonFeignDto dto);
}
