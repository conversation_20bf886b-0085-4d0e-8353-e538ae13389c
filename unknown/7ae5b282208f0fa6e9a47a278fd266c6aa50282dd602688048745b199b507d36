package com.jp.med.core.modules.sys.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.core.modules.sys.dto.SysDataRoleDto;
import com.jp.med.core.modules.sys.service.read.SysDataRoleReadService;
import com.jp.med.core.modules.sys.service.write.SysDataRoleWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 系统数据角色
 * <AUTHOR>
 * @email -
 * @date 2023-04-23 18:57:37
 */
@Api(value = "系统数据角色", tags = "系统数据角色")
@RestController
@RequestMapping("sys/dataRole")
public class SysDataRoleController {

    @Autowired
    private SysDataRoleReadService sysDataRoleReadService;

    @Autowired
    private SysDataRoleWriteService sysDataRoleWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询系统数据角色")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody SysDataRoleDto dto){
        dto.setSqlAutowiredHospitalCondition(true);
        return CommonResult.success(sysDataRoleReadService.queryList(dto));
    }

    @ApiOperation("新增系统数据角色")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody SysDataRoleDto dto){
        dto.setCreateUserId(dto.getSysUser().getId());
        sysDataRoleWriteService.save(dto);
        return CommonResult.success();
    }

    @ApiOperation("修改系统数据角色")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody SysDataRoleDto dto){
        sysDataRoleWriteService.updateById(dto);
        return CommonResult.success();
    }

    @ApiOperation("删除系统数据角色")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody SysDataRoleDto dto){
        sysDataRoleWriteService.delete(dto);
        return CommonResult.success();
    }

    @ApiOperation("保存数据角色菜单")
    @PostMapping("/saveDataRoleMenu")
    public CommonResult<?> saveDataRoleMenu(@RequestBody SysDataRoleDto dto){
        sysDataRoleWriteService.saveDataRoleMenu(dto);
        return CommonResult.success();
    }

    @ApiOperation("查询数据角色菜单")
    @PostMapping("/queryDataRoleMenu")
    public CommonResult<?> queryDataRoleMenu(@RequestBody SysDataRoleDto dto){
        return CommonResult.success(sysDataRoleReadService.queryDataRoleMenu(dto));
    }
}
