package com.jp.med.core.modules.sys.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.common.dto.prcs.PrcsDto;
import com.jp.med.common.vo.PrcsVo;

import java.util.List;

/**
 * 流程主表
 * <AUTHOR>
 * @email -
 * @date 2023-12-13 16:02:39
 */
public interface SysPrcsReadService extends IService<PrcsDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<PrcsVo> queryList(PrcsDto dto);
}

