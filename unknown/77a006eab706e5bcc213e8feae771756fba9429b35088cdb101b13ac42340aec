<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jp.med.core.modules.sys.mapper.read.SysDictReadMapper">

    <!-- 查询用户角色 -->
    <select id="querySysDict" resultType="com.jp.med.common.entity.sys.SysDict">
        SELECT
        id,code_value as value,
        code_label as label,
        code_type as type,
        code_desc as description,
        code_sort as sort,
        create_time as createTime,
        remarks
        FROM sys_dict
        <where>
            <if test="label!=null and label!='' ">
                AND (code_label like CONCAT('%',#{label,jdbcType=VARCHAR},'%')
                OR code_desc LIKE CONCAT('%',#{label,jdbcType=VARCHAR},'%')
                OR code_type LIKE CONCAT('%',#{label,jdbcType=VARCHAR},'%'))
            </if>
            <if test="startTime!=null and startTime!=''
         and endTime!=null and endTime!='' ">
                AND create_time between to_timestamp( CONCAT( #{startTime,jdbcType=VARCHAR}, ' 00:00:00'), 'yyyy-mm-dd
                HH24:mi:ss')
                and to_timestamp( CONCAT(#{endTime,jdbcType=VARCHAR}, ' 23:59:59'), 'yyyy-mm-dd HH24:mi:ss')
            </if>
            <if test="description!=null and description!='' ">
                AND code_desc LIKE CONCAT('%',#{description,jdbcType=VARCHAR},'%')
            </if>
            <if test="type!=null and type!='' ">
                AND code_type LIKE CONCAT('%',#{type,jdbcType=VARCHAR},'%')
            </if>
            and (del_flag is null or del_flag != -1)
        </where>
        ORDER BY code_type,code_sort
    </select>

</mapper>
