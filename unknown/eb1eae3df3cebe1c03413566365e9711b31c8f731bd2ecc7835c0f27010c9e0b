package com.jp.med.core.modules.sys.service.read.impl;

import com.github.pagehelper.PageHelper;
import com.jp.med.core.modules.sys.dto.SysCollRoleDto;
import com.jp.med.core.modules.sys.mapper.read.SysCollRoleReadMapper;
import com.jp.med.core.modules.sys.service.read.SysCollRoleReadService;
import com.jp.med.core.modules.sys.vo.SysCollRoleVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
public class SysCollRoleReadServiceImpl extends ServiceImpl<SysCollRoleReadMapper, SysCollRoleDto> implements SysCollRoleReadService {

    @Autowired
    private SysCollRoleReadMapper coreSysCollRoleReadMapper;

    //TODO 归集角色下面的角色和数据角色查询
    @Override
    public List<SysCollRoleVo> queryList(SysCollRoleDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        List<SysCollRoleVo> sysCollRoleVos = coreSysCollRoleReadMapper.queryList(dto);
        sysCollRoleVos.forEach(sysCollRoleVo -> {
            if (StringUtils.isNotEmpty(sysCollRoleVo.getMenuRoleIds())) {
                sysCollRoleVo.setRoleIds(convertIds(sysCollRoleVo.getMenuRoleIds().split(",")));
            }
            if (StringUtils.isNotEmpty(sysCollRoleVo.getDataRoleIds())) {
                sysCollRoleVo.setRoleDataIds(convertIds(sysCollRoleVo.getDataRoleIds().split(",")));
            }
        });
        return sysCollRoleVos;
    }

    public static List<Long> convertIds(String[] sids){
        List<Long> ids = new ArrayList<>();
        for (String sid : sids) {
            ids.add(Long.parseLong(sid));
        }
        return ids;
    }

}
