package com.jp.med.core.modules.sys.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.core.modules.sys.dto.SysDataRoleDto;
import com.jp.med.core.modules.sys.entity.SysDataRoleMenuEntity;
import com.jp.med.core.modules.sys.entity.SysSystem;
import com.jp.med.core.modules.sys.mapper.read.SysDataRoleReadMapper;
import com.jp.med.core.modules.sys.mapper.read.SysSystemReadMapper;
import com.jp.med.core.modules.sys.service.read.SysDataRoleReadService;
import com.jp.med.core.modules.sys.vo.SysDataRoleVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
public class SysDataRoleReadServiceImpl extends ServiceImpl<SysDataRoleReadMapper, SysDataRoleDto> implements SysDataRoleReadService {

    @Autowired
    private SysDataRoleReadMapper sysDataRoleReadMapper;

    @Autowired
    private SysSystemReadMapper sysSystemReadMapper;

    @Override
    public List<SysDataRoleVo> queryList(SysDataRoleDto dto) {
        List<SysDataRoleVo> result = new ArrayList<>();
        List<SysDataRoleVo> sysDataRoleVoList = sysDataRoleReadMapper.queryList(dto);
        List<SysSystem> sysSystems = sysSystemReadMapper.querySystemConfig(new SysSystem());
        Map<Long, List<SysDataRoleVo>> collect = sysDataRoleVoList.stream().collect(Collectors.groupingBy(SysDataRoleVo::getSystemId));
        collect.forEach((id, roles) -> {
            Optional<SysSystem> first = sysSystems.stream().filter(sysSystem -> sysSystem.getId().equals(id)).findFirst();
            SysSystem system = first.orElse(new SysSystem());
            Random rand = new Random();
            int tempId = rand.nextInt(90000000) + 10000000;
            SysDataRoleVo role = new SysDataRoleVo();
            role.setNotShowActions(true);
            role.setRoleName(system.getSysName());
            role.setKey(system.getSysName());
            role.setRoleDataId((long) tempId);
            roles.forEach(r -> r.setSystemId(id));
            role.setChildren(roles);
            role.setSystemId(id);
            result.add(role);
        });
        return result;
    }

    @Override
    public List<SysDataRoleMenuEntity> queryDataRoleMenu(SysDataRoleDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        dto.setRoleDataIds(Collections.singletonList(dto.getRoleDataId()));
        return sysDataRoleReadMapper.queryDataRoleMenu(dto);
    }
}
