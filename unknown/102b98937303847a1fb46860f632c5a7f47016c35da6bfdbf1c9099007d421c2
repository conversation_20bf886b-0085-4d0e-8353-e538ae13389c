package com.jp.med.core.modules.sys.mapper.read;

import com.jp.med.core.modules.sys.dto.SysOrgDto;
import com.jp.med.core.modules.sys.entity.SysOrgEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 组织架构
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-03-16 17:25:48
 */
@Mapper
public interface SysOrgReadMapper extends BaseMapper<SysOrgDto> {
    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<SysOrgEntity> queryList(SysOrgDto dto);

    /**
     * 查询组织架构
     * @param dto
     * @return
     */
    List<SysOrgEntity> queryOrg(SysOrgDto dto);

    /**
     * 查询组织架构-根据组织名称
     * @param dto
     * @return
     */
    List<SysOrgEntity> queryOrgByName(SysOrgDto dto);

    /**
     * 查询组织架构Ids
     * @param dto
     * @return
     */
    List<SysOrgEntity> queryOrgIds(SysOrgDto dto);

    /**
     *查询组织架构下拉
     * @param dto
     * @return
     */
    List<SysOrgEntity> queryOrgTree(SysOrgDto dto);
}
