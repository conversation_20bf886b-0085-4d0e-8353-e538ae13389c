package com.jp.med.core.modules.user.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 用户签章
 * <AUTHOR>
 * @email -
 * @date 2024-01-16 20:01:51
 */
@Data
@TableName("sys_user_sign")
public class CoreSysUserSignEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 用户名 */
	@TableField("username")
	private String username;

	/** 签章文件 */
	@TableField("att")
	private String att;

	/** 签章文件名称 */
	@TableField("att_name")
	private String attName;

	/** 描述 */
	@TableField("desc")
	private String desc;

	/** 签章类型(1:签名 2:公章) */
	@TableField("sign_type")
	private String signType;

	/** 创建时间 */
	@TableField("create_time")
	private String createTime;

	/** 有效标志 */
	@TableField("active_flag")
	private String activeFlag;

}
