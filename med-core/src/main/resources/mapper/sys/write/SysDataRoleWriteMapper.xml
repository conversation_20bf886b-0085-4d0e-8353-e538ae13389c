<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.core.modules.sys.mapper.write.SysDataRoleWriteMapper">

    <!-- 保存数据角色菜单 -->
    <insert id="saveDataRoleMenu">
        INSERT INTO sys_data_role_menu(role_data_id,
                                       menu_id,
                                       data_auth_level)
        VALUES (#{roleDataId,jdbcType=INTEGER},
                #{menuId,jdbcType=INTEGER},
                #{dataAuthLevel,jdbcType=VARCHAR})
    </insert>

    <!-- 新增用户数据角色 -->
    <insert id="saveUserDataRole">
        INSERT INTO sys_user_data_role(user_id, role_data_id)
        VALUES
        <foreach collection="roleDataIds" item="roleId" separator=",">
            (#{id,jdbcType=INTEGER}, #{roleId,jdbcType=INTEGER})
        </foreach>
    </insert>
    <update id="batchSaveDataRole">
        INSERT INTO sys_user_data_role(user_id, role_data_id)
        VALUES
        <foreach collection="userDtos" item="item" separator=",">
            <foreach collection="item.roleDataIds" item="roleDataId" separator=",">
                (#{item.id,jdbcType=BIGINT}, #{roleDataId,jdbcType=INTEGER})
            </foreach>
        </foreach>
    </update>

    <!-- 删除数据角色菜单 -->
    <delete id="deleteDataRoleMenu">
        DELETE
        FROM sys_data_role_menu rm
            USING sys_menu m
        WHERE rm.menu_id = m.menu_id
          AND rm.role_data_id = #{roleDataId,jdbcType=INTEGER}
        <if test="sysType != null and sysType != ''">
            AND m.sys_type = #{sysType,jdbcType=VARCHAR}
        </if>
    </delete>

    <!-- 删除用户数据角色 -->
    <delete id="deleteUserDataRole">
        DELETE FROM sys_user_data_role WHERE user_id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 通过数据角色id删除用户角色 -->
    <delete id="deleteUserDataRoleByRoleId">
        DELETE FROM sys_user_data_role WHERE role_data_id = #{roleDataId,jdbcType=INTEGER}
    </delete>

</mapper>
