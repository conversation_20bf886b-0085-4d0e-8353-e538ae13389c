<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jp.med.core.modules.sys.mapper.read.SysSystemReadMapper">

    <!-- 查询系统 -->
    <select id="querySystemConfig" resultType="com.jp.med.core.modules.sys.entity.SysSystem">
        SELECT id,
               sys_name AS sysName,
               sys_desc AS sysDesc,
               sys_url AS sysUrl,
               status
        FROM sys_system
        WHERE status = 1
        <if test="username != null and username != ''">
            AND id IN (
                select c.system_id from sys_user a
                inner join sys_user_role b
                on a.user_id = b.user_id
                inner join sys_role c
                on b.role_id = c.role_id
                where username = #{username,jdbcType=VARCHAR}
            )
        </if>
        <if test="sysName != null and sysName != ''">
            AND sys_name LIKE CONCAT('%',#{sysName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="sysUrl != null and sysUrl != ''">
            AND sys_url = #{sysUrl,jdbcType=VARCHAR}
        </if>
        <if test="id != null">
            AND id = #{id,jdbcType=BIGINT}
        </if>
        ORDER BY sort
    </select>

</mapper>
