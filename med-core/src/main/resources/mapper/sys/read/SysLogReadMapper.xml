<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.core.modules.sys.mapper.read.SysLogReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.core.modules.sys.vo.SysLogVo" id="logMap">
        <result property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="operation" column="operation"/>
        <result property="method" column="method"/>
        <result property="params" column="params"/>
        <result property="time" column="time"/>
        <result property="ip" column="ip"/>
        <result property="createDate" column="create_date"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.core.modules.sys.vo.SysLogVo">
        select
            id as id,
            username as username,
            operation as operation,
            method as method,
            params as params,
            time as time,
            ip as ip,
            create_date as createDate
        from sys_log
        order by create_date DESC
    </select>

</mapper>
