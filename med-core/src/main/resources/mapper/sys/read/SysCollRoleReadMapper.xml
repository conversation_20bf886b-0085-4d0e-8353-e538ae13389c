<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.core.modules.sys.mapper.read.SysCollRoleReadMapper">

    <select id="queryList" resultType="com.jp.med.core.modules.sys.vo.SysCollRoleVo">
        select
            a.id as id,
            a.role_name as roleName,
            a.dscr as dscr,
            a.crter as crter,
            a.create_time as createTime,
            b.menuRoleName,
            b.menuRoleIds,
            c.dataRoleName,
            c.dataRoleIds
        from sys_coll_role a
        left join (
            select string_agg( distinct cast(b.remark as varchar) ,',') as menuRoleName,
                   string_agg( distinct cast(b.role_id as varchar) ,',') as menuRoleIds,
                   a.sys_coll_role_id
            from sys_coll_role_detl a
            left join sys_role b
            on a.role_id = b.role_id
            group by a.sys_coll_role_id
        ) b
        on a.id = b.sys_coll_role_id
        left join (
            select string_agg( distinct cast(b.remark as varchar) ,',') as dataRoleName,
                   string_agg( distinct cast(b.role_data_id as varchar) ,',') as dataRoleIds,
                   a.sys_coll_role_id
            from sys_coll_data_role_detl a
            left join sys_data_role b
            on a.role_data_id = b.role_data_id
            group by a.sys_coll_role_id
        ) c
        on a.id = c.sys_coll_role_id
        <where>
            <if test="id != null ">
                AND a.id = #{id,jdbcType=INTEGER}
            </if>
            <if test="roleName != null and roleName != ''">
                AND a.role_name = #{roleName,jdbcType=VARCHAR}
            </if>
            <if test="dscr != null and dscr != ''">
                AND a.dscr = #{dscr,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

</mapper>
