<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.core.modules.sys.mapper.read.SysConfigReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.common.entity.sys.SysConfigEntity" id="configMap">
        <result property="id" column="id"/>
        <result property="paramKey" column="param_key"/>
        <result property="paramValue" column="param_value"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.common.entity.sys.SysConfigEntity">
        select
            id as id,
            param_key as paramKey,
            param_value as paramValue,
            status as status,
            remark as remark
        from sys_config
        <where>
            <if test="paramKey != null and paramKey != ''">
                and param_key like concat('%',#{paramKey,jdbcType=VARCHAR},'%')
            </if>
            <if test="status != null and status != ''">
                and status = #{status,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

</mapper>