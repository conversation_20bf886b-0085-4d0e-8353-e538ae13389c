<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.core.modules.sys.mapper.read.SysDataRoleReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.core.modules.sys.vo.SysDataRoleVo" id="dataRoleMap">
        <result property="roleDataId" column="role_data_id"/>
        <result property="roleName" column="role_name"/>
        <result property="remark" column="remark"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.core.modules.sys.vo.SysDataRoleVo">
        select
            role_data_id as roleDataId,
            role_name as roleName,
            remark as remark,
            create_user_id as createUserId,
            create_time as createTime,
            system_id as systemId
        from sys_data_role
    </select>

    <!-- 查询数据角色菜单 -->
    <select id="queryDataRoleMenu"
            resultType="com.jp.med.core.modules.sys.entity.SysDataRoleMenuEntity">
        SELECT a.role_data_id AS roleDataId,
               a.menu_id AS menuId,
               a.data_auth_level AS dataAuthLevel,
               b.url AS menuUrl,
               b.sys_type AS sysType
        FROM sys_data_role_menu a
        INNER JOIN sys_menu b
        ON a.menu_id = b.menu_id
        INNER JOIN sys_data_role c
        ON a.role_data_id = c.role_data_id
        WHERE a.role_data_id
        <foreach collection="roleDataIds" item="id" open="IN (" close=")" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
        <if test="systemId != null and systemId != ''">
            AND c.system_id = #{systemId,jdbcType=VARCHAR}
        </if>
    </select>

</mapper>
