<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.core.modules.sys.mapper.read.CoreNeedorbugReadMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.core.modules.sys.vo.CoreNeedorbugVo" id="needorbugMap">
        <result property="id" column="id"/>
        <result property="empId" column="emp_id"/>
        <result property="orgId" column="org_id"/>
        <result property="email" column="email"/>
        <result property="phone" column="phone"/>
        <result property="systemId" column="system_id"/>
        <result property="menuId" column="menu_id"/>
        <result property="forwardType" column="forward_type"/>
        <result property="title" column="title"/>
        <result property="description" column="description"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="createdAt" column="created_at"/>
        <result property="activeFlag" column="active_flag"/>
        <result property="engId" column="eng_id"/>
        <result property="reply" column="reply"/>
        <result property="finishTime" column="finish_time"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="att" column="att"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.core.modules.sys.vo.CoreNeedorbugVo">
        select
        a.id as id,
        a.id as key,
        a.emp_id as empId,
        b.emp_name as empName,
        c.org_name as orgName,
        b.email as email,
        b.phone as phone,
        a.system_id as systemId,
        a.menu_id as menuId,
        a.forward_type as forwardType,
        a.title as title,
        a.description as description,
        a.remark as remark,
        a.status as status,
        a.created_at as createdAt,
        a.active_flag as activeFlag,
        d.eng_name as engName,
        d.eng_phone as engPhone,
        d.eng_email as engEmail,
        d.eng_wechat as engWechat,
        d.eng_team as engTeam,
        d.remark as engRemark,
        d.status as engStatus,
        a.reply as reply,
        a.finish_time as finishTime,
        a.hospital_id as hospitalId,
        a.status_time as statusTime,
        a.att as att,
        a.progress as progress
        from sys_needorbug a
        LEFT JOIN
        (SELECT
        id,
        emp_name,
        email,
        phone,
        is_deleted
        FROM
        hrm_employee_info
        WHERE is_deleted =0
        ) b
        ON a.emp_id = b.id
        LEFT JOIN
        (SELECT
        org_id,
        org_name,
        active_flag
        FROM
        hrm_org
        WHERE active_flag ='1') c
        ON a.org_id =c.org_id
        LEFT JOIN
        (SELECT * FROM
        sys_engineer_info
        )d
        on a.eng_id =d.id
        <where>
            ( a.active_flag is NULL OR a.active_flag != 1)
            <if test="checkEmpId != null and checkEmpId != ''">
                AND a.emp_id = #{checkEmpId,jdbcType=INTEGER}
            </if>
            <if test="forwardType != null and forwardType != ''">
                AND a.forward_type = #{forwardType,jdbcType=INTEGER}
            </if>
            <if test="status != null and status != ''">
                AND a.status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="systemId != null">
                AND a.system_id = #{systemId}
            </if>
        </where>
        ORDER BY TO_TIMESTAMP(a.created_at, 'YYYY-MM-DD HH24:MI:SS') DESC

    </select>

</mapper>
