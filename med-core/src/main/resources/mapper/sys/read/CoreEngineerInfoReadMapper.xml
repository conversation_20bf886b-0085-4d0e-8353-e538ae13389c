<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.core.modules.sys.mapper.read.CoreEngineerInfoReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.core.modules.sys.vo.CoreEngineerInfoVo" id="engineerInfoMap">
        <result property="id" column="id"/>
        <result property="engName" column="eng_name"/>
        <result property="engPhone" column="eng_phone"/>
        <result property="engEmail" column="eng_email"/>
        <result property="engWechat" column="eng_wechat"/>
        <result property="engTeam" column="eng_team"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="activeFlag" column="active_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="hospitalId" column="hospital_id"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.core.modules.sys.vo.CoreEngineerInfoVo">
        select
            id as id,
            eng_name as engName,
            eng_phone as engPhone,
            eng_email as engEmail,
            eng_wechat as engWechat,
            eng_team as engTeam,
            remark as remark,
            status as status,
            active_flag as activeFlag,
            create_time as createTime,
            update_time as updateTime,
            delete_time as deleteTime,
            hospital_id as hospitalId
        from sys_engineer_info
        <where>
            ( active_flag is NULL OR active_flag != 1)
        <if test="id != null and id !=''">
            AND id = #{id,jdbcType=INTEGER}
        </if>
        </where>
    </select>

</mapper>
