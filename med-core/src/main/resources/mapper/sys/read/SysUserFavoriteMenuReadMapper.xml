<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.core.modules.sys.mapper.read.SysUserFavoriteMenuReadMapper">

    <!-- 结果映射 -->
    <resultMap type="com.jp.med.core.modules.sys.vo.SysUserFavoriteMenuVo" id="userFavoriteMenuMap">
        <result property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="menuPath" column="menu_path"/>
        <result property="menuName" column="menu_name"/>
        <result property="menuIcon" column="menu_icon"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="systemId" column="system_id"/>
        <result property="menuId" column="menu_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
        <result property="isPinned" column="is_pinned"/>
        <result property="pinOrder" column="pin_order"/>
        <result property="remark" column="remark"/>
        <result property="warnNum" column="warn_num"/>
    </resultMap>

    <!-- 查询用户常用菜单列表 -->
    <select id="queryUserFavoriteMenus" resultMap="userFavoriteMenuMap">
        SELECT 
            ufm.id,
            ufm.username,
            ufm.menu_path,
            ufm.menu_name,
            ufm.menu_icon,
            ufm.sort_order,
            ufm.system_id,
            ufm.menu_id,
            ufm.create_time,
            ufm.update_time,
            ufm.status,
            ufm.is_pinned,
            ufm.pin_order,
            ufm.remark,
            COALESCE(fa.warn_num, 0) as warn_num
        FROM sys_user_favorite_menu ufm
        LEFT JOIN (
            SELECT path, SUM(COALESCE(warn_num, 0)) as warn_num
            FROM oa_fast_arrive 
            GROUP BY path
        ) fa ON ufm.menu_path = fa.path
        <where>
            ufm.status = '1'
            <if test="username != null and username != ''">
                AND ufm.username = #{username}
            </if>
            <if test="systemId != null">
                AND ufm.system_id = #{systemId}
            </if>
            <if test="menuPath != null and menuPath != ''">
                AND ufm.menu_path LIKE CONCAT('%', #{menuPath}, '%')
            </if>
            <if test="menuName != null and menuName != ''">
                AND ufm.menu_name LIKE CONCAT('%', #{menuName}, '%')
            </if>
        </where>
        ORDER BY ufm.sort_order ASC, ufm.create_time DESC
    </select>

    <!-- 获取当前用户的常用菜单 -->
    <select id="getCurrentUserFavoriteMenus" resultMap="userFavoriteMenuMap">
        SELECT 
            ufm.id,
            ufm.username,
            ufm.menu_path,
            ufm.menu_name,
            ufm.menu_icon,
            ufm.sort_order,
            ufm.system_id,
            ufm.menu_id,
            ufm.create_time,
            ufm.update_time,
            ufm.status,
            ufm.is_pinned,
            ufm.pin_order,
            ufm.remark,
            COALESCE(fa.warn_num, 0) as warn_num
        FROM sys_user_favorite_menu ufm
        LEFT JOIN (
            SELECT path, SUM(COALESCE(warn_num, 0)) as warn_num
            FROM oa_fast_arrive 
            GROUP BY path
        ) fa ON ufm.menu_path = fa.path
        WHERE ufm.username = #{username} 
        AND ufm.status = '1'
        ORDER BY ufm.sort_order ASC, ufm.create_time DESC
    </select>

    <!-- 检查指定菜单是否已被用户收藏 -->
    <select id="checkMenuFavorited" resultType="int">
        SELECT COUNT(1)
        FROM sys_user_favorite_menu
        WHERE username = #{username}
        AND menu_path = #{menuPath}
        AND status = '1'
    </select>

    <!-- 获取用户常用菜单数量 -->
    <select id="getUserFavoriteMenuCount" resultType="int">
        SELECT COUNT(1)
        FROM sys_user_favorite_menu
        WHERE username = #{username}
        AND status = '1'
    </select>

    <!-- 获取当前用户的PIN菜单 -->
    <select id="getCurrentUserPinnedMenus" resultMap="userFavoriteMenuMap">
        SELECT
            ufm.id,
            ufm.username,
            ufm.menu_path,
            ufm.menu_name,
            ufm.menu_icon,
            ufm.sort_order,
            ufm.system_id,
            ufm.menu_id,
            ufm.create_time,
            ufm.update_time,
            ufm.status,
            ufm.is_pinned,
            ufm.pin_order,
            ufm.remark,
            COALESCE(fa.warn_num, 0) as warn_num
        FROM sys_user_favorite_menu ufm
        LEFT JOIN (
            SELECT path, SUM(COALESCE(warn_num, 0)) as warn_num
            FROM oa_fast_arrive
            GROUP BY path
        ) fa ON ufm.menu_path = fa.path
        WHERE ufm.username = #{username}
        AND ufm.status = '1'
        AND ufm.is_pinned = '1'
        ORDER BY ufm.pin_order ASC, ufm.create_time DESC
    </select>

    <!-- 获取用户PIN菜单数量 -->
    <select id="getUserPinnedMenuCount" resultType="int">
        SELECT COUNT(1)
        FROM sys_user_favorite_menu
        WHERE username = #{username}
        AND status = '1'
        AND is_pinned = '1'
    </select>

    <!-- 检查指定菜单是否已被用户PIN -->
    <select id="checkMenuPinned" resultType="int">
        SELECT COUNT(1)
        FROM sys_user_favorite_menu
        WHERE username = #{username}
        AND menu_path = #{menuPath}
        AND status = '1'
        AND is_pinned = '1'
    </select>

</mapper>
