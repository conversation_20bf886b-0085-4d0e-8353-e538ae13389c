package com.jp.med.core.modules.sys.controller;

import com.jp.med.core.modules.sys.dto.SysCollRoleDto;
import com.jp.med.core.modules.sys.service.read.SysCollRoleReadService;
import com.jp.med.core.modules.sys.service.write.SysCollRoleWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 角色归集
 * <AUTHOR>
 * @email -
 * @date 2023-12-05 17:11:17
 */
@Api(value = "角色归集", tags = "角色归集")
@RestController
@RequestMapping("sysCollRole")
public class SysCollRoleController {

    @Autowired
    private SysCollRoleReadService sysCollRoleReadService;

    @Autowired
    private SysCollRoleWriteService sysCollRoleWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询角色归集")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody SysCollRoleDto dto){
        return CommonResult.success(sysCollRoleReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增角色归集")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody SysCollRoleDto dto){
        sysCollRoleWriteService.saveCollRole(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改角色归集")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody SysCollRoleDto dto){
        sysCollRoleWriteService.updateCollRole(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除角色归集")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody SysCollRoleDto dto){
        sysCollRoleWriteService.removeCollRole(dto);
        return CommonResult.success();
    }

}
