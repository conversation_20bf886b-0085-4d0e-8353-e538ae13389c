package com.jp.med.core.modules.common.controller;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.util.HttpRequestUtil;
import com.jp.med.common.util.OFD2PDFUtils;
import com.jp.med.common.util.OSSUtil;
import com.jp.med.common.util.PdfUtil;
import com.jp.med.common.vo.OCRResVo;
import com.jp.med.core.modules.common.dto.CommonCoreDto;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/11 13:44
 * @description:
 */
@RestController
@RequestMapping("/commonCoreController")
public class CommonCoreController {


    /**
     * 文件上传
     *
     * @param files
     */
    @PostMapping("/uploadTempFile")
    public CommonResult<?> upload(MultipartFile[] files) {
        try {
            ArrayList<String> filePaths = new ArrayList<>();
            //类型
            for (MultipartFile file : files) {
                OSSUtil.uploadFile(OSSConst.BUCKET_CORE, file, OSSConst.CORE_TEMP_INFO_PATH + file.getOriginalFilename(), file.getContentType());
                filePaths.add(OSSConst.CORE_TEMP_INFO_PATH + file.getOriginalFilename());
            }
            return CommonResult.success(filePaths);
        } catch (Exception e) {
            return CommonResult.failed(e.getMessage());
        }
    }

    /**
     * 删除
     *
     * @param fileName
     */
    @DeleteMapping("/delete")
    public void delete(@RequestParam("fileName") String fileName) {
        OSSUtil.removeFile(OSSConst.BUCKET_HRM, fileName);
    }

    /**
     * 获取文件信息
     *
     * @param fileName
     * @return
     */
    @GetMapping("/info")
    public CommonResult<?> getFileStatusInfo(@RequestParam("fileName") String fileName) {
        return CommonResult.success(OSSUtil.getFileStatusInfo(OSSConst.BUCKET_HRM, fileName));
    }

    /**
     * 获取文件外链
     *
     * @param dto
     * @return
     */
    @PostMapping("/url")
    public CommonResult<?> getPresignedObjectUrl(@RequestBody CommonCoreDto dto) {
        if (StringUtils.isNotEmpty(dto.getPath()) && StringUtils.isNotEmpty(dto.getBucket())) {
            boolean objectExist = OSSUtil.isObjectExist(dto.getBucket(), dto.getPath());
            if (objectExist) {
                String pathLowerCase = dto.getPath().toLowerCase();
                if (pathLowerCase.endsWith(".ofd")) {
                    return CommonResult.success(OFD2PDFUtils.getOfdPdfPresignedObjectUrl(dto.getBucket(), dto.getPath()));
                }
                if (pathLowerCase.endsWith(".ppt") || pathLowerCase.endsWith(".pptx")) {
                    return CommonResult.success(OFD2PDFUtils.getPPT2PdfPresignedObjectUrl(dto.getBucket(), dto.getPath()));
                }
                if (pathLowerCase.endsWith(".doc") || pathLowerCase.endsWith(".docx")) {
                    return CommonResult.success(OFD2PDFUtils.getWord2PdfPresignedObjectUrl(dto.getBucket(), dto.getPath()));
                }
                return CommonResult.success(OSSUtil.getPresignedObjectUrl(dto.getBucket(), dto.getPath()));
            } else {
                return CommonResult.success("-1");
            }
        }
        return CommonResult.success();
    }

    /**
     * 获取源文件外链
     *
     * @param dto
     * @return
     */
    @PostMapping("/sourceFileUrl")
    public CommonResult<?> getSourceFileUrl(@RequestBody CommonCoreDto dto) {
        if (StringUtils.isNotEmpty(dto.getPath()) && StringUtils.isNotEmpty(dto.getBucket())) {
            boolean objectExist = OSSUtil.isObjectExist(dto.getBucket(), dto.getPath());
            if (objectExist) {
                return CommonResult.success(OSSUtil.getPresignedObjectUrl(dto.getBucket(), dto.getPath()));
            } else {
                return CommonResult.success("-1");
            }
        }
        return CommonResult.success();
    }

    /**
     * 测试
     *
     * @param url 请求地址
     * @return
     */
    @PostMapping("/test")
    public CommonResult<?> test(@RequestParam("url") String url) {
        Map<String, Object> params = new HashMap<>();
        params.put("img_path", url);
        String result = HttpRequestUtil.post(params, "http://127.0.0.1:7410/identify");
        result = result.substring(1, result.length() - 1).replaceAll("\\\\", "");
        OCRResVo ocrResVo = JSON.parseObject(result, OCRResVo.class);
        return CommonResult.success(ocrResVo);
    }

    public String unescapeJsonString(String json) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode node = objectMapper.readTree("\"" + json + "\"");
            return node.asText();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 文件下载
     */
    @PostMapping("/download")
    public void download(@RequestBody CommonCoreDto dto, HttpServletResponse response) {
        try {
            InputStream fileInputStream = OSSUtil.getObject(dto.getBucket(), dto.getPath());
            response.setHeader("Content-Disposition", "attachment;filename=" + dto.getPath());
            response.setContentType("application/force-download");
            response.setCharacterEncoding("UTF-8");
            IOUtils.copy(fileInputStream, response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取文件外链
     *
     * @param fileName
     * @return
     */
    @GetMapping("/temp")
    public CommonResult<?> temp(@RequestParam("fileName") String fileName) {
//        ObjectWriteResponse dir = OSSUtil.createDir(OSSConst.BUCKET_HRM, fileName);
        Map<String, Object> data = new HashMap<>();
        data.put("empName", "张三1111");
        data.put("sex", "男");
        data.put("mz", "神族");
        data.put("icdCard", "51080211111111");
        data.put("birthday", "2001.03");
        data.put("startTime", "2023.10");
        data.put("education", "大学");
        data.put("graduateTime", "2023.06");
        data.put("graduateSchool", "xxx");
        data.put("major", "xxx");
        data.put("employeeType", "xxx");
        data.put("startTime", "2023.10");
        data.put("communicationAddress", "德阳中江县");
        data.put("phone", "110");
        data.put("contactName", "xxx");
        data.put("relation", "110");
        data.put("address", "德阳中江县");
        String filePath = null;
        try {
            filePath = PdfUtil.readOssRender(OSSConst.BUCKET_HRM, "template/zprymb.docx", data);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return CommonResult.success(filePath);
    }
}
