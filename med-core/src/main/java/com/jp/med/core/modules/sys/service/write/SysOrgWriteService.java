package com.jp.med.core.modules.sys.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.core.modules.sys.dto.SysOrgDto;

/**
 * 组织架构
 *
 * <AUTHOR>
 * @email -
 * @date 2023-03-16 17:25:48
 */
public interface SysOrgWriteService extends IService<SysOrgDto> {
    /**
     * 添加组织架构
     * @param dto
     */
     void insertSysOrg(SysOrgDto dto);

    /**
     * 修改组织架构
     * @param dto
     */
    void updateSysOrg(SysOrgDto dto);

    /**
     * 删除组织架构
     * @param dto
     */
    void deleteSysOrg(SysOrgDto dto);

    /**
     * 上传组织架构信息
     * @param file
     */
    void uploadData(SysOrgDto file);

    /**
     * 图模型数据保存
     * @param dto
     */
    void graphModelDataSave(SysOrgDto dto);

    /**
     * 同步 hrm 的组织架构
     * @param dto
     */
    void synHrmOrg(SysOrgDto dto);
}

