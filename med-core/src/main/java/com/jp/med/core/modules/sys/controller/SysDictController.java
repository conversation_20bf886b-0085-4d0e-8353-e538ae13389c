package com.jp.med.core.modules.sys.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.entity.sys.SysDict;
import com.jp.med.common.util.DictUtil;
import com.jp.med.core.config.GlobalInitConfig;
import com.jp.med.core.modules.sys.service.read.SysDictReadService;
import com.jp.med.core.modules.sys.service.write.SysDictWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @date: 2023-03-07
 * @author: yzx_w
 *
 */

@Api(value = "字典管理", tags = {"字典管理"})
@RestController
@RequestMapping(value = "sys/dict")
public class SysDictController {

    @Autowired
    private SysDictReadService sysDictReadService;

    @Autowired
    private SysDictWriteService sysDictWriteService;

    @Autowired
    private GlobalInitConfig globalInit;

    @ApiOperation("查询字典")
    @PostMapping("/querySysDict")
    public CommonResult<IPage<SysDict>> querySysDict(@RequestBody SysDict dto){
        return CommonResult.paging(sysDictReadService.querySysDict(dto));
    }

    @ApiOperation("增加字典")
    @PostMapping("/addSysDict")
    public CommonResult<?> addSysDict(@RequestBody SysDict dto){
        sysDictWriteService.save(dto);
        return CommonResult.success();
    }

    @ApiOperation("删除字典")
    @DeleteMapping ("/delSysDict")
    public CommonResult<?> delSysDict(@RequestBody SysDict dto){
        sysDictWriteService.removeById(dto);
        return CommonResult.success();
    }

    @ApiOperation("修改字典")
    @PutMapping("/updateSysDict")
    public CommonResult<?> updateSysDict(@RequestBody SysDict dto){
        sysDictWriteService.updateSysDict(dto);
        return CommonResult.success();
    }

    @ApiOperation("查询缓存中的所有字典数据")
    @PostMapping("/queryByCache")
    public CommonResult<List<SysDict>> queryByCache(@RequestBody SysDict dto){
        List<SysDict> all = DictUtil.all();
        if (all == null || all.isEmpty()) {
            globalInit.dict("重新加载字典成功");
            all = DictUtil.all();
        }
        return CommonResult.success(all);
    }

    @ApiOperation("刷新缓存")
    @PostMapping("/refreshCache")
    public CommonResult<?> refreshCache(@RequestBody SysDict dto){
        globalInit.dict("刷新缓存成功");
        return CommonResult.success();
    }
}
