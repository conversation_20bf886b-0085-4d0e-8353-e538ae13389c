package com.jp.med.core.modules.sys.mapper.write;

import com.jp.med.common.entity.sys.SysLog;
import com.jp.med.core.modules.sys.dto.SysLogDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 系统日志
 * <AUTHOR>
 * @email -
 * @date 2023-04-26 11:21:39
 */
@Mapper
public interface SysLogWriteMapper extends BaseMapper<SysLogDto> {

    /**
     * 保存系统日志
     * @param sysLog
     */
    void saveLog(SysLog sysLog);
}
