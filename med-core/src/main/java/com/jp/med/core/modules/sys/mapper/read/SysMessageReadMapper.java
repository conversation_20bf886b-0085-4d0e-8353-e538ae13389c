package com.jp.med.core.modules.sys.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.dto.message.SysMessageDto;
import com.jp.med.core.modules.sys.vo.SysMessageVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 系统公告
 */
@Mapper
public interface SysMessageReadMapper extends BaseMapper<SysMessageDto> {
    /**
     * 查询所有消息
     * @param dto
     * @return
    */
    List<SysMessageVo> queryMessageList(SysMessageDto dto);


    /**
     * 获取用户未读消息数量
     * @param username
     * @return
     */
    List<Map<String, Object>> selectMessageNumByType(String username);

}
