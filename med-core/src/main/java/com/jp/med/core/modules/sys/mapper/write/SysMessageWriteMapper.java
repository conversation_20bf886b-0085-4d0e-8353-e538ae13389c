package com.jp.med.core.modules.sys.mapper.write;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.dto.message.SysMessageDto;
import com.jp.med.core.modules.user.vo.UserVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统公告
 */
@Mapper
public interface SysMessageWriteMapper extends BaseMapper<SysMessageDto> {

    void addMessage(@Param("list") List<SysMessageDto> dto);

    List<UserVo> queryReceiveUser();


    /**
     * 根据用户名查询用户clientId
     * @param username
     * @return
     */
    String queryReceiveUserClientId(@Param("username") String username);
}
