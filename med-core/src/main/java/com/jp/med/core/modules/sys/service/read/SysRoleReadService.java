package com.jp.med.core.modules.sys.service.read;

import com.jp.med.common.entity.sys.SysMenu;
import com.jp.med.common.entity.sys.SysRole;
import com.jp.med.core.modules.sys.dto.RevisePermissionDto;
import com.jp.med.core.modules.sys.dto.SysRoleDto;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/28 10:50
 * @description:
 */
public interface SysRoleReadService {

    /**
     * 查询系统角色
     * @param dto
     * @return
     */
    List<SysRole> querySysRole(SysRoleDto dto);

    /**
     * 修改角色权限
     * @param dto
     */
    List<SysMenu> queryPermission(RevisePermissionDto dto);
}
