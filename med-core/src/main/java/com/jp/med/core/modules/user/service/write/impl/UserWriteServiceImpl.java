package com.jp.med.core.modules.user.service.write.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.constant.AppMessageConst;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.app.AppMsgDto;
import com.jp.med.common.dto.common.CommonFeignDto;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.common.entity.payload.SystemPayload;
import com.jp.med.common.entity.user.SysUser;
import com.jp.med.common.feign.AppMessageFeignService;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.DateUtil;
import com.jp.med.common.util.RedisUtil;
import com.jp.med.common.util.ULIDUtil;
import com.jp.med.core.modules.sys.dto.SysCollRoleDto;
import com.jp.med.core.modules.sys.mapper.read.SysCollRoleReadMapper;
import com.jp.med.core.modules.sys.mapper.write.SysDataRoleWriteMapper;
import com.jp.med.core.modules.sys.mapper.write.SysRoleWriteMapper;
import com.jp.med.core.modules.sys.service.read.SysCollRoleReadService;
import com.jp.med.core.modules.sys.service.write.SysCollRoleWriteService;
import com.jp.med.core.modules.sys.vo.SysCollRoleVo;
import com.jp.med.core.modules.user.dto.UserDto;
import com.jp.med.core.modules.user.entity.SysUserTokenEntity;
import com.jp.med.core.modules.user.feign.gateway.EncryFeignService;
import com.jp.med.core.modules.user.feign.hrm.HrmFeignService;
import com.jp.med.core.modules.user.mapper.read.UserReadMapper;
import com.jp.med.core.modules.user.mapper.write.UserWriteMapper;
import com.jp.med.core.modules.user.service.write.UserWriteService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.jp.med.core.modules.sys.service.read.impl.SysCollRoleReadServiceImpl.convertIds;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/16 16:18
 * @description:
 */
@Service
@Transactional
public class UserWriteServiceImpl extends ServiceImpl<UserWriteMapper, SysUser> implements UserWriteService {

    @Autowired
    private UserWriteMapper userWriteMapper;

    @Autowired
    private SysRoleWriteMapper sysRoleWriteMapper;

    @Autowired
    private SysDataRoleWriteMapper sysDataRoleWriteMapper;

    @Autowired
    private UserReadMapper userReadMapper;

    @Autowired
    private HrmFeignService hrmFeignService;

    @Autowired
    private EncryFeignService encryFeignService;

    @Autowired
    private AppMessageFeignService appMessageFeignService;
    @Autowired
    private SysCollRoleReadMapper coreSysCollRoleReadMapper;

    @Autowired
    private SysCollRoleWriteService collRoleWriteService;

    /** status， 1：征程， 0：禁用 */
    private final int USER_STATUS = 1;
    @Override
    public void updateUserPwd(SysUser dto) {
       UpdateWrapper<SysUser> updateWrapper=new UpdateWrapper<>();
       updateWrapper.eq("user_id",dto.getId());
       this.update(dto,updateWrapper);
    }

    @Override
    public void saveUser(UserDto dto) {
        dto.setStatus(USER_STATUS);
        dto.setCreateUserId(dto.getSysUser().getId());
        if (dto.getCollRoleIds() != null) {
            dto.setCollRoleIdsStr(dto.getCollRoleIds().stream().map(Object::toString).collect(Collectors.joining(",")));
        }
        userWriteMapper.saveUser(dto);
        //保存用户角色
        if (dto.getRoleIds() != null && dto.getRoleIds().length > 0) {
            sysRoleWriteMapper.saveRole(dto);
        }
        if (dto.getRoleDataIds() != null && dto.getRoleDataIds().length > 0) {
            sysDataRoleWriteMapper.saveUserDataRole(dto);
        }
    }

    /**
     * 注册的供应商用户没有创建者createUserid的信息
     * 另外供应商的归集角色定死的： id:24   PURMS-SUPPLIER-USER
     * */

    @Override
    public void saveSupplierUser(UserDto dto) {
        //用户状态(待定,审核通过再解除禁用?)
        dto.setStatus(USER_STATUS);
        dto.setUsername(dto.getUsername());

        //设置归集角色集
        ArrayList<Integer> ids = new ArrayList<>();
        ids.add(Integer.parseInt(MedConst.PURMS_SUPPLIER_USER_COROLE));
        dto.setCollRoleIds(ids);
        //供应商的归集角色定死了
        if (CollectionUtil.isNotEmpty(dto.getCollRoleIds())) {
            dto.setCollRoleIdsStr(dto.getCollRoleIds().stream().map(Object::toString).collect(Collectors.joining(",")));
        }
        userWriteMapper.saveUser(dto);
        //根据归集角色id查 角色id和数据角色id
        SysCollRoleDto sysCollRoleDto = new SysCollRoleDto();
        sysCollRoleDto.setId(Long.parseLong(MedConst.PURMS_SUPPLIER_USER_COROLE));

        List<SysCollRoleVo> sysCollRoleVos = coreSysCollRoleReadMapper.queryList(sysCollRoleDto);
        //处理
        sysCollRoleVos.forEach(sysCollRoleVo -> {
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(sysCollRoleVo.getMenuRoleIds())) {
                sysCollRoleVo.setRoleIds(convertIds(sysCollRoleVo.getMenuRoleIds().split(",")));
            }
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(sysCollRoleVo.getDataRoleIds())) {
                sysCollRoleVo.setRoleDataIds(convertIds(sysCollRoleVo.getDataRoleIds().split(",")));
            }
        });
//        sysCollRoleDto.setRoleIds(sysCollRoleVos.get(0).getRoleIds());
//        sysCollRoleDto.setRoleDataIds(sysCollRoleVos.get(0).getRoleDataIds());
//
        SysCollRoleVo collRoleVo = sysCollRoleVos.get(0);

        // 转换为int[]数组
        int[] roleIdsConverted = collRoleVo.getRoleIds().stream()
                .mapToInt(longValue -> {
                    if (longValue < Integer.MIN_VALUE || longValue > Integer.MAX_VALUE) {
                        throw new IllegalArgumentException("Long值超出了int的范围");
                    }
                    return longValue.intValue();
                })
                .toArray();

        int[] roleDataIdsConverted = collRoleVo.getRoleDataIds().stream()
                .mapToInt(longValue -> {
                    if (longValue < Integer.MIN_VALUE || longValue > Integer.MAX_VALUE) {
                        throw new IllegalArgumentException("Long值超出了int的范围");
                    }
                    return longValue.intValue();
                })
                .toArray();

        dto.setRoleIds(roleIdsConverted);
        dto.setRoleDataIds(roleDataIdsConverted);

        updateUser(dto);
//
//        //保存用户角色
//        if (dto.getRoleIds() != null && dto.getRoleIds().length > 0) {
//            sysRoleWriteMapper.saveRole(dto);
//        }
//        if (dto.getRoleDataIds() != null && dto.getRoleDataIds().length > 0) {
//            sysDataRoleWriteMapper.saveUserDataRole(dto);
//        }
    }

    @Override
    public void deleteUser(UserDto dto) {
        userWriteMapper.deleteUser(dto);
        //删除用户角色
        sysRoleWriteMapper.deleteRole(dto);
    }

    @Override
    public void updateUser(UserDto dto) {
        if (CollectionUtil.isNotEmpty(dto.getCollRoleIds())) {
            dto.setCollRoleIdsStr(dto.getCollRoleIds().stream().map(Object::toString).collect(Collectors.joining(",")));
        }
        userWriteMapper.updateUser(dto);
        if (dto.getRoleIds() != null && dto.getRoleIds().length > 0) {
            //修改用户角色
            sysRoleWriteMapper.deleteRole(dto);
            sysRoleWriteMapper.saveRole(dto);

            // 删除和新增用户数据角色
            sysDataRoleWriteMapper.deleteUserDataRole(dto);
            sysDataRoleWriteMapper.saveUserDataRole(dto);
        }
    }

    @Override
    public void saveUserToken(CommonFeignDto dto) {
        SysUser sysUser = BeanUtil.mapToBean((Map<?, ?>) dto.get(MedConst.HEADER_USER_INFO_KEY), SysUser.class, true);
        SysUserTokenEntity sysUserTokenEntity = BeanUtil.mapToBean(dto, SysUserTokenEntity.class, true);
        sysUserTokenEntity.setUserId(sysUser.getUsername());
        sysUserTokenEntity.setUlid(ULIDUtil.generate());
        userWriteMapper.saveUserToken(sysUserTokenEntity);
        resetUserError(sysUser.getUsername());
    }

    @Override
    public void deleteUserToken(CommonFeignDto dto) {
        SysUser sysUser = BeanUtil.mapToBean((Map<?, ?>) dto.get(MedConst.HEADER_USER_INFO_KEY), SysUser.class, true);
        userWriteMapper.deleteUserToken(sysUser.getUsername());
    }

    @Override
    public void deleteUserToken(UserDto dto) {
        List<String> ids = new ArrayList<>();
        if (dto.getExitAllUser()) {
            dto.setSqlAutowiredHospitalCondition(true);
            List<SysUser> sysUsers = userReadMapper.queryUserList(dto);
            for (SysUser sysUser : sysUsers) ids.add(sysUser.getUname());
        } else {
            if (CollectionUtil.isNotEmpty(Arrays.asList(dto.getIds()))) {
                ids = Arrays.asList(dto.getIds());
            }
        }
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        // 查询所有用户token
        List<SysUserTokenEntity> sysUserTokenEntities = userReadMapper.queryUserToken(ids);
        sysUserTokenEntities.forEach(userToken -> {
            if(RedisUtil.hasKey(userToken.getToken())){
                RedisUtil.del(userToken.getToken());
            }
        });
        ids.forEach(id -> userWriteMapper.deleteUserToken(id));
//        BatchUtil.batch("deleteUserToken", ids, UserWriteMapper.class);
    }

    @Override
    public void synchEmpToUser(UserDto dto) {
        CommonFeignResult empResult = hrmFeignService.queryEmpInfoByIds(dto.getEmpIds());
        List<LinkedHashMap<String,Object>> data = (List<LinkedHashMap<String, Object>>) empResult.get("data");
        List<SysUser> dtos = new ArrayList<>();
        List<String> usernameList = new ArrayList<>();
        data.forEach(map -> {
            setEmpInfo(map,dtos, dto, usernameList);
        });
        if (dto.getBatchModify()) {
            UserDto ud = new UserDto();
            ud.setUsernameList(usernameList);
            // 查询用户
            List<SysUser> sysUsers = userReadMapper.queryUserList(ud);
            if (CollectionUtil.isNotEmpty(sysUsers)) {
                List<UserDto> btos = new ArrayList<>();
                dtos.forEach(s1 -> {
                    sysUsers.forEach(s2 -> {
                        if (s1.getUname().equals(s2.getUname())) {
                            s1.setId(s2.getId());
                            UserDto d = new UserDto();
                            d.setId(s2.getId());
                            d.setCollRoleIdsStr(dto.getCollRoleIds().stream().map(Object::toString).collect(Collectors.joining(",")));
                            btos.add(d);
                        }
                    });
                });
                // 更新用户归集角色
                BatchUtil.batch("updateUser", btos, UserWriteMapper.class);
                // 删除用户菜单角色和数据角色
                BatchUtil.batch("deleteRole", btos, SysRoleWriteMapper.class);
                BatchUtil.batch("deleteUserDataRole", btos, SysDataRoleWriteMapper.class);
            }
        } else{
            //批量新增用户
            saveBatch(dtos);
        }
        //批量新增 角色和数据角色
        saveBatchRoleAndDataRole(dtos,dto);
    }

    private void saveBatchRoleAndDataRole(List<SysUser> dtos,UserDto dto) {
        //角色
        List<UserDto> userDtos = new ArrayList<>();
        dtos.forEach(item -> {
            UserDto userDto = new UserDto();
            userDto.setId(item.getId());
            userDto.setRoleIds(dto.getRoleIds());
            userDto.setRoleDataIds(dto.getRoleDataIds());
            userDtos.add(userDto);
        });
        if (dto.getRoleIds().length > 0) {
            sysRoleWriteMapper.batchSaveRole(userDtos);
        }
        //数据角色
        if (dto.getRoleDataIds().length > 0) {
            sysDataRoleWriteMapper.batchSaveDataRole(userDtos);
        }
    }


    private void setEmpInfo (LinkedHashMap<String,Object> map, List<SysUser> dtos, UserDto dto, List<String> usernameList) {
        SysUser user = new SysUser();
        HashMap pwdMap= encryFeignService.encryPwd("123456");
        user.setPassword((String)pwdMap.get(CommonFeignResult.DATA_KEY));
        user.setStatus(Integer.valueOf(MedConst.TYPE_1));
        user.setUname((String) map.get("empCode"));
        user.setNickname((String) map.get("empName"));
        user.setEmail((String) map.get("email"));
        user.setMobile((String) map.get("phone"));
        user.setHospitalId((String) map.get("hospitalId"));
        user.setOrgId((String) map.get("orgId"));
        if (CollectionUtil.isNotEmpty(dto.getCollRoleIds())) {
            user.setCollRoleIdsStr(dto.getCollRoleIds().stream().map(Object::toString).collect(Collectors.joining(",")));
        }
        usernameList.add(user.getUname());
        dtos.add(user);
    }

    @Override
    public Integer updatePasswordErrorNum(String username) {
        if (StringUtils.isEmpty(username)) {
            return -1;
        }
        Integer maxErrorNum = 10;
        Integer t = userWriteMapper.updatePasswordErrorNum(username);
        SysUser sysUser = userReadMapper.getUserByUsername(username);
        if (sysUser != null && sysUser.getPasswordErrorNum() != null && sysUser.getPasswordErrorNum().equals(maxErrorNum)) {
            UserDto userDto = new UserDto();
            userDto.setId(sysUser.getId());
            userDto.setIsLock(MedConst.TYPE_1);
            userWriteMapper.updateUser(userDto);

            // 推送消息到手机
            SystemPayload systemPayload = new SystemPayload();
            AppMsgDto msgDto = new AppMsgDto();
            msgDto.setMsgTypeCode(AppMessageConst.MSG_SYSTEM);
            msgDto.setTitle(sysUser.getNickname()+"("+sysUser.getUsername()+")账户被锁");
            msgDto.setContent("["+sysUser.getNickname() + "-"+sysUser.getUsername()+"]账户被锁");
            msgDto.setRead(MedConst.TYPE_0);
            msgDto.setCrter("admin");
            msgDto.setCreateTime(DateUtil.getCurrentTime(null));
            msgDto.setRecer("0131");
            // 设置payload
            systemPayload.setPageUrl("/pages/view/sys/user/index");
            Map<String,Object> extData = new HashMap<>();
            extData.put("lockUsername", sysUser.getUsername());
            systemPayload.setExtData(extData);
            msgDto.setPayload(JSON.toJSONString(systemPayload));
            appMessageFeignService.save(msgDto);
        }
        return sysUser != null && sysUser.getPasswordErrorNum() != null ? maxErrorNum - sysUser.getPasswordErrorNum() : 0;
    }

    private void resetUserError(String username) {
        if (StringUtils.isEmpty(username)) {
            return;
        }
        SysUser sysUser = userReadMapper.getUserByUsername(username);
        if (sysUser != null) {
            UserDto userDto = new UserDto();
            userDto.setId(sysUser.getId());
            userDto.setPasswordErrorNum(0);
            userDto.setIsLock(MedConst.TYPE_0);
            userWriteMapper.updateUser(userDto);
        }
    }
}
