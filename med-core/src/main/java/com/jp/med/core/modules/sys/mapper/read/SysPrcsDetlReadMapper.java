package com.jp.med.core.modules.sys.mapper.read;

import com.jp.med.common.dto.prcs.PrcsDetlDto;
import com.jp.med.common.vo.PrcsDetlVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 流程详情
 * <AUTHOR>
 * @email -
 * @date 2023-12-13 16:21:53
 */
@Mapper
public interface SysPrcsDetlReadMapper extends BaseMapper<PrcsDetlDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<PrcsDetlVo> queryList(PrcsDetlDto dto);
}
