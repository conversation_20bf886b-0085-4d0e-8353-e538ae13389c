package com.jp.med.core.modules.oaTask.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.core.modules.oaTask.dto.FastArriveDto;
import com.jp.med.core.modules.oaTask.mapper.read.FastArriveReadMapper;
import com.jp.med.core.modules.oaTask.service.read.FastArriveReadService;
import com.jp.med.core.modules.oaTask.vo.FastArriveVo;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Transactional(readOnly = true)
@Service
public class FastArriveReadServiceImpl extends ServiceImpl<FastArriveReadMapper, FastArriveDto> implements FastArriveReadService {

    @Autowired
    private FastArriveReadMapper fastArriveReadMapper;

    @Override
    public List<FastArriveVo> queryList(FastArriveDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return fastArriveReadMapper.queryList(dto);
    }

    @Override
    public List<FastArriveVo> oaTaskLst(FastArriveDto dto) {
        dto.setUserId(dto.getSysUser().getId());
        return fastArriveReadMapper.oaTaskLst(dto);
    }

}
