package com.jp.med.core.modules.common.feign;

import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.entity.common.CommonFeignResult;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@RefreshScope
@FeignClient(name = "RmsAuditFeignService",url = "${custom.gateway.med-rms-service-uri}")
public interface RmsAuditFeignService {

	/**
	 * 审核完成
	 * @param dto
	 * @return
	 */
	@PostMapping("/rmsAuditRcdfm/complete")
	CommonFeignResult complete(@RequestBody AuditDetail dto);
}
