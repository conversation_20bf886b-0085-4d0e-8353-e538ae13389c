package com.jp.med.core.modules.sys.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.context.UserContext;
import com.jp.med.core.modules.sys.dto.SysUserFavoriteMenuDto;
import com.jp.med.core.modules.sys.mapper.read.SysUserFavoriteMenuReadMapper;
import com.jp.med.core.modules.sys.service.read.SysUserFavoriteMenuReadService;
import com.jp.med.core.modules.sys.vo.SysUserFavoriteMenuVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 用户常用菜单读取服务实现
 * <AUTHOR>
 * @email -
 * @date 2025-01-20
 */
@Service
@Transactional(readOnly = true)
public class SysUserFavoriteMenuReadServiceImpl 
    extends ServiceImpl<SysUserFavoriteMenuReadMapper, SysUserFavoriteMenuDto> 
    implements SysUserFavoriteMenuReadService {

    @Autowired
    private SysUserFavoriteMenuReadMapper sysUserFavoriteMenuReadMapper;

    @Override
    public List<SysUserFavoriteMenuVo> queryUserFavoriteMenus(SysUserFavoriteMenuDto dto) {
        return sysUserFavoriteMenuReadMapper.queryUserFavoriteMenus(dto);
    }

    @Override
    public List<SysUserFavoriteMenuVo> getCurrentUserFavoriteMenus() {
        String username = getCurrentUsername();
        return sysUserFavoriteMenuReadMapper.getCurrentUserFavoriteMenus(username);
    }

    @Override
    public boolean isMenuFavorited(String menuPath) {
        String username = getCurrentUsername();
        int count = sysUserFavoriteMenuReadMapper.checkMenuFavorited(username, menuPath);
        return count > 0;
    }

    @Override
    public int getUserFavoriteMenuCount(String username) {
        return sysUserFavoriteMenuReadMapper.getUserFavoriteMenuCount(username);
    }

    @Override
    public List<SysUserFavoriteMenuVo> getCurrentUserPinnedMenus() {
        String username = getCurrentUsername();
        return sysUserFavoriteMenuReadMapper.getCurrentUserPinnedMenus(username);
    }

    @Override
    public int getUserPinnedMenuCount(String username) {
        return sysUserFavoriteMenuReadMapper.getUserPinnedMenuCount(username);
    }

    @Override
    public boolean isMenuPinned(String menuPath) {
        String username = getCurrentUsername();
        int count = sysUserFavoriteMenuReadMapper.checkMenuPinned(username, menuPath);
        return count > 0;
    }

    /**
     * 获取当前用户名
     * @return 用户名
     */
    private String getCurrentUsername() {
        try {
            return UserContext.getEmpCode();
        } catch (Exception e) {
            throw new RuntimeException("获取用户上下文失败: " + e.getMessage(), e);
        }
    }
}
