package com.jp.med.core.modules.sys.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

import java.util.List;

/**
 * 角色归集
 * <AUTHOR>
 * @email -
 * @date 2023-12-05 17:11:17
 */
@Data
@TableName("sys_coll_role" )
public class SysCollRoleDto extends CommonQueryDto {

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 角色名称 */
    @TableField("role_name")
    private String roleName;

    /** 说明 */
    @TableField("dscr")
    private String dscr;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 角色id */
    @TableField(exist = false)
    private List<Long> roleIds;

    /** 数据角色id */
    @TableField(exist = false)
    private List<Long> roleDataIds;

}
