package com.jp.med.core.modules.sys.service.write.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.util.DateUtil;
import com.jp.med.common.util.OSSUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.core.modules.sys.mapper.write.CoreNeedorbugWriteMapper;
import com.jp.med.core.modules.sys.dto.CoreNeedorbugDto;
import com.jp.med.core.modules.sys.service.write.CoreNeedorbugWriteService;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Array;
import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 需求orBug记录
 *
 * <AUTHOR>
 * @email -
 * @date 2024-01-16 15:20:11
 */
@Service
@Transactional(readOnly = false)
public class CoreNeedorbugWriteServiceImpl extends ServiceImpl<CoreNeedorbugWriteMapper, CoreNeedorbugDto> implements CoreNeedorbugWriteService {

    @Autowired
    private CoreNeedorbugWriteMapper needorbugWriteMapper;


    @Override
    public void saveDto(CoreNeedorbugDto dto) {
        if (StringUtils.isNotEmpty(dto.getDtoJson())) {
            CoreNeedorbugDto coreNeedorbugDto = JSONObject.parseObject(dto.getDtoJson(), CoreNeedorbugDto.class);
            coreNeedorbugDto.setAttFiles(dto.getAttFiles());
            coreNeedorbugDto.setSysUser(dto.getSysUser());
            dto = coreNeedorbugDto;
        }
        //设置提出人的信息
        if (dto.getSysUser() != null) {
            if (MedConst.DEV_NAME.equals(dto.getSysUser().getUsername())) {
                //如果是管理员新增提出需求，则empid设为null
                dto.setEmpId(null);
                dto.setOrgId(null);
            } else {
                dto.setEmpId(dto.getSysUser().getHrmUser().getEmpId().intValue());
                dto.setOrgId(dto.getSysUser().getHrmUser().getHrmOrgId());
            }
        }
        //设置组织
        dto.setHospitalId(dto.getSysUser().getHospitalId());
        //设置流程状态为1： 等待中
        dto.setStatus(MedConst.FLOW_STATUS_1);
        //创建时间
        dto.setCreatedAt(DateUtil.getCurrentTime("yyyy-MM-dd"));
        //设置状态
        dto.setActiveFlag(Integer.parseInt(MedConst.ACTIVE_FLAG_0));

        dto.setStatusTime(DateUtil.getCurrentTime("yyyy-MM-dd"));
        // 保存报告信息
        // 目标菜单可以多选
        if (dto.getMenuIds() != null && dto.getMenuIds().length > 0) {
            String[] itemCodeList = dto.getMenuIds();
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < itemCodeList.length; i++) {
                sb.append(itemCodeList[i]);
                if (i < itemCodeList.length - 1) {
                    sb.append(",");
                }
            }
            dto.setMenuId(sb.toString());

        }
        //文件上传
        if (dto.getAttFiles() != null && CollectionUtil.isNotEmpty(dto.getAttFiles())) {
            StringBuilder attStr = new StringBuilder();
            dto.getAttFiles().forEach(a -> {
                String attFilePath = OSSUtil.uploadFile(OSSConst.BUCKET_CORE, "needOrBug/", a);
                attStr.append(attFilePath);
                attStr.append(",");
            });
            dto.setAtt(attStr.toString());
        }
        //保存
        needorbugWriteMapper.insert(dto);
    }

    @Override
    public void modifyById(CoreNeedorbugDto dto) {
        if (StringUtils.isNotEmpty(dto.getDtoJson())) {
            CoreNeedorbugDto coreNeedorbugDto = JSONObject.parseObject(dto.getDtoJson(), CoreNeedorbugDto.class);
            coreNeedorbugDto.setAttFiles(dto.getAttFiles());
            coreNeedorbugDto.setSysUser(dto.getSysUser());
            dto = coreNeedorbugDto;
        }
        //设置提出人的信息
        if (dto.getSysUser() != null) {
            if (MedConst.DEV_NAME.equals(dto.getSysUser().getUsername())) {
                //如果是管理员新增提出需求，则empid设为null
                dto.setEmpId(null);
                dto.setOrgId(null);
            } else {
                dto.setEmpId(dto.getSysUser().getHrmUser().getEmpId().intValue());
                dto.setOrgId(dto.getSysUser().getHrmUser().getHrmOrgId());
            }
        }
        //设置组织
        dto.setHospitalId(dto.getSysUser().getHospitalId());
        //创建时间
        dto.setCreatedAt(DateUtil.getCurrentTime("yyyy-MM-dd"));
        //设置状态
        dto.setActiveFlag(Integer.parseInt(MedConst.ACTIVE_FLAG_0));

        dto.setStatus(dto.getStatus());
        if ("6".equals(dto.getStatus()) || "5".equals(dto.getStatus())) {
            //如果状态为已完成设置进度为100%
            if ("6".equals(dto.getStatus())) {
                dto.setProgress(100);
            } else if ("6".equals(dto.getStatus()) && !Objects.isNull(dto.getProgress())) {
                dto.setProgress(dto.getProgress());
                dto.setStatus("5");
            } else {
                dto.setProgress(dto.getProgress());
            }

        } else {
            dto.setProgress(0);
        }
        dto.setStatusTime(DateUtil.getCurrentTime("yyyy-MM-dd"));

        //完成时间:如果progress大于等于100写入当前时间
        if (!Objects.isNull(dto.getProgress()) && dto.getProgress() >= 100) {
            dto.setStatus("6");
            dto.setFinishTime(DateUtil.getCurrentTime("yyyy-MM-dd"));
        } else {
            dto.setFinishTime("");
        }
        // 保存报告信息
        // 目标菜单可以多选
        if (dto.getMenuIds() != null && dto.getMenuIds().length > 0) {
            String[] itemCodeList = dto.getMenuIds();
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < itemCodeList.length; i++) {
                sb.append(itemCodeList[i]);
                if (i < itemCodeList.length - 1) {
                    sb.append(",");
                }
            }
            dto.setMenuId(sb.toString());
        }

        //文件上传
        if (dto.getAttFiles() != null && CollectionUtil.isNotEmpty(dto.getAttFiles())) {
            StringBuilder attStr = new StringBuilder();
            dto.getAttFiles().forEach(a -> {
                String attFilePath = OSSUtil.uploadFile(OSSConst.BUCKET_CORE, "needOrBug/", a);
                attStr.append(attFilePath);
                attStr.append(",");
            });
            dto.setAtt(attStr.toString());
        }
        needorbugWriteMapper.updateById(dto);
    }

    @Override
    public void deleteById(CoreNeedorbugDto dto) {
        dto.setActiveFlag(Integer.parseInt(MedConst.ACTIVE_FLAG_1));
        needorbugWriteMapper.updateById(dto);
    }
}
