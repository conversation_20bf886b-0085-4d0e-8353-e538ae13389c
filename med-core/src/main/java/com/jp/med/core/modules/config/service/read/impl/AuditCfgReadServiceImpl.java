package com.jp.med.core.modules.config.service.read.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.core.modules.config.dto.AuditCfgDto;
import com.jp.med.core.modules.config.mapper.read.AuditCfgReadMapper;
import com.jp.med.core.modules.config.service.read.AuditCfgReadService;
import com.jp.med.core.modules.config.vo.AuditCfgVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
public class AuditCfgReadServiceImpl extends ServiceImpl<AuditCfgReadMapper, AuditCfgDto> implements AuditCfgReadService {

    @Autowired
    private AuditCfgReadMapper auditCfgReadMapper;

    @Autowired
    private AuditCfgReadServiceImpl auditCfgReadService;

    @Override
    public List<AuditCfgVo> queryList(AuditCfgDto dto) {
        List<AuditCfgVo> auditCfgVos = auditCfgReadMapper.queryList(dto);
        if (CollectionUtil.isNotEmpty(auditCfgVos)) {
            List<Long> auditIds = new ArrayList<>();
            auditCfgVos.forEach(vo -> auditIds.add(vo.getId()));
            List<AuditDetail> details = auditCfgReadMapper.queryDetails(auditIds);
            Map<Long, List<AuditDetail>> map = details.stream().collect(Collectors.groupingBy(AuditDetail::getAuditCfgId));
            auditCfgVos.forEach(vo -> {
                List<AuditDetail> details1 = map.get(vo.getId());
                List<AuditDetail> collect = details1.stream().sorted(Comparator.comparingInt(AuditDetail::getChkSeq)).collect(Collectors.toList());
                vo.setDetails(collect);
            });
        }
        return auditCfgVos;
    }


}
