package com.jp.med.core.modules.user.service.read.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.common.CommonFeignDto;
import com.jp.med.common.dto.emp.EmpEmployeeInfoDto;
import com.jp.med.common.dto.emp.HrmOrgDto;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.common.entity.emp.EmpEmployeeInfoEntity;
import com.jp.med.common.entity.sys.SysMenu;
import com.jp.med.common.entity.sys.SysRole;
import com.jp.med.common.entity.user.BmsUser;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.entity.user.SysUser;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.RedisUtil;
import com.jp.med.core.modules.sys.dto.SysDataRoleDto;
import com.jp.med.core.modules.sys.dto.SysOrgDto;
import com.jp.med.core.modules.sys.entity.SysDataRoleMenuEntity;
import com.jp.med.core.modules.sys.mapper.read.SysDataRoleReadMapper;
import com.jp.med.core.modules.sys.mapper.read.SysOrgReadMapper;
import com.jp.med.core.modules.sys.service.read.impl.SysCollRoleReadServiceImpl;
import com.jp.med.core.modules.user.dto.UserDto;
import com.jp.med.core.modules.user.entity.CustomUserDetails;
import com.jp.med.core.modules.user.feign.bms.BmsOrgFeignService;
import com.jp.med.core.modules.user.feign.hrm.HrmFeignService;
import com.jp.med.core.modules.user.mapper.read.UserReadMapper;
import com.jp.med.core.modules.user.service.read.UserReadService;
import com.jp.med.core.modules.user.vo.Transfer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/16 11:46
 * @description: 用户接口
 */
@Slf4j
@Service
@Transactional(readOnly = true)
public class UserReadServiceImpl implements UserReadService {

    private final String ORG_ID_KEY = "orgId";

    @Autowired
    private UserReadMapper userReadMapper;

    @Autowired
    private BmsOrgFeignService bmsOrgFeignService;

    @Autowired
    private HrmFeignService hrmFeignService;

    @Autowired
    private SysDataRoleReadMapper sysDataRoleReadMapper;

    @Autowired
    private SysOrgReadMapper sysOrgReadMapper;

    @Override
    public CustomUserDetails queryUserByUsername(String username) {
        CustomUserDetails userDetails = new CustomUserDetails();
        SysUser user = userReadMapper.getUserByUsername(username);
        setDataRoleMenuMapping(user);
        setOtherSystemInfo(user);
        List<SysRole> permissionList = userReadMapper.getPermissionsByUserId(user.getId());
        user.setPermissionList(permissionList);
        userDetails.setSysUser(user);
        userDetails.setPermissionList(permissionList);
        return userDetails;
    }

    /**
     * 设置数据角色菜单映射
     *
     * @param user 用户信息
     */
    private void setDataRoleMenuMapping(SysUser user) {
        SysDataRoleDto sysDataRoleDto = new SysDataRoleDto();
        String[] sids = user.getRoleDataIdStr().split(",");
        List<Long> ids = new ArrayList<>(sids.length);
        Arrays.stream(sids).forEach(id -> ids.add(Long.parseLong(id)));
        sysDataRoleDto.setRoleDataIds(ids);
        List<SysDataRoleMenuEntity> sysDataRoleMenuEntities = sysDataRoleReadMapper.queryDataRoleMenu(sysDataRoleDto);
        Map<String, String> mapping = new HashMap<>();
        if (CollectionUtil.isNotEmpty(sysDataRoleMenuEntities)) {
            sysDataRoleMenuEntities
                    .stream().filter(entity -> entity.getDataAuthLevel() != null)
                    .forEach(entity -> {
                String key = entity.getSysType()+"/" + entity.getMenuUrl();
                if (mapping.containsKey(key)) {
                    String value = mapping.get(key);
                    if (Integer.parseInt(value) > Integer.parseInt(entity.getDataAuthLevel())) {
                        mapping.put(key, entity.getDataAuthLevel());
                    }
                } else {
                    mapping.put(key, entity.getDataAuthLevel());
                }
            });
        }
        user.setDataRoleUrlMapping(mapping);
    }

    /**
     * 设置其他系统信息
     *
     * @param user 当前用户
     */
    private void setOtherSystemInfo(SysUser user) {
        setBmsInfo(user);
        setHrmInfo(user);
    }

    private void setHrmInfo(SysUser user) {
        try {
            CommonFeignResult commonFeignResult = hrmFeignService.queryByEmpCode(user.getUsername());
            Map map = (Map) commonFeignResult.get(CommonFeignResult.DATA_KEY);
            EmpEmployeeInfoEntity data = BeanUtil.mapToBean(map, EmpEmployeeInfoEntity.class, true);
            HrmUser hrmUser = new HrmUser();
            if (ObjectUtil.isNotNull(data)) {
                BeanUtils.copyProperties(data, hrmUser);
                hrmUser.setAjtOrgIds(data.getAjtOrgIdsStr());
                hrmUser.setHrmOrgName(data.getOrgName());
                hrmUser.setHrmOrgId(data.getOrgId());
                hrmUser.setEmpId(data.getId());
                String oriOrg = data.getOriOrg();
                if (StringUtils.isNotEmpty(oriOrg)){
                    hrmUser.setOrlOrgIds(Arrays.asList(oriOrg.split(",")));
                }
            }
            user.setHrmUser(hrmUser);
        } catch (Exception e) {
            e.printStackTrace();
            log.warn("HRM-设置人力资源信息系失败");
        }
    }

    /**
     * 设置bms信息
     *
     * @param user
     */
    private void setBmsInfo(SysUser user) {
        try {
            CommonFeignResult commonFeignResult = bmsOrgFeignService.queryOrgByEmpCode(user.getUsername());
            Map<?, ?> data = (Map<?, ?>) commonFeignResult.get(CommonFeignResult.DATA_KEY);
            BmsUser bmsUser = new BmsUser();
            if (ObjectUtil.isNotNull(data)) {
                bmsUser.setMapping((Map<String, String>) data.get("mapping"));
            }
            user.setBmsUser(bmsUser);
        } catch (Exception e) {
            log.warn("BMS-设置预算信息失败");
        }
    }

    @Override
    public List<String> queryUserAuthByUsername(CommonFeignDto dto) {
        List<String> roles = new ArrayList<>();
        List<SysRole> sysRoles = userReadMapper.queryUserAuthByUsername(dto);
        if (sysRoles != null) {
            sysRoles.forEach(sysRole -> roles.add(sysRole.getName()));
        }
        return roles;
    }

    @Override
    public List<SysMenu> queryUserMenu(UserDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        List<SysMenu> menus;
        // 如果是管理员则读取所有菜单
        if (dto.getSysUser() != null) {
            if (MedConst.DEV_NAME.equals(dto.getSysUser().getUsername())) {
                dto.setType(MedConst.TYPE_2);
            }
        }
        // type1为用户菜单，type2为系统菜单
        if (MedConst.TYPE_1.equals(dto.getType())) {
            menus = userReadMapper.queryUserMenu(dto);
        } else {
            if (ObjectUtil.isNotNull(dto.getSysMenu()) && StringUtils.isNotEmpty(dto.getSysMenu().getMenuName())) {
                menus = userReadMapper.querySysMenuByCon(dto);
            } else {
                menus = userReadMapper.querySysMenu(dto);
            }
        }
        // 顶层menu
        List<SysMenu> topMenus = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(menus)) {
            menus.forEach(menu -> {
                if (menu.getParentId() == null) {
                    topMenus.add(menu);
                }
            });
            generateMenuTree(topMenus, menus, dto);
        }
        return topMenus;
    }

    @Override
    public List<SysUser> queryUserList(UserDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<SysUser> sysUsers = userReadMapper.queryUserList(dto);
        if (sysUsers != null) {
            for (SysUser sysUser : sysUsers) {
                if (StringUtils.isNotEmpty(sysUser.getCollRoleIdsStr())) {
                    sysUser.setCollRoleIds(SysCollRoleReadServiceImpl.convertIds(sysUser.getCollRoleIdsStr().split(",")));
                }
            }
        }
        return sysUsers;
    }

    @Override
    public Boolean existUser(UserDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        SysUser sysUser = userReadMapper.existUser(dto);
        return sysUser != null;
    }

    @Override
    public List<Transfer> genSynUserOptions(UserDto dto) {
        //系统
        SysOrgDto sysOrgDto = new SysOrgDto();
        BeanUtils.copyProperties(dto, sysOrgDto);
        UserDto userDto = new UserDto();
        BeanUtils.copyProperties(dto, userDto);
        //用户
        List<SysUser> sysUsers = userReadMapper.queryUserList(userDto);

        //组织
        Map<String, List<HrmOrgDto>> hrmOrgOrgParentIdMap = dto.getHrmOrgDtos().stream().collect(Collectors.groupingBy(hrmOrgDto -> {
            if (StringUtils.isEmpty(hrmOrgDto.getOrgParentId())) {
                return "";
            }
            return hrmOrgDto.getOrgParentId();
        }));
        //员工
        Map<String, List<EmpEmployeeInfoDto>> hrmEmpOrgIdMap = dto.getHrmEmps().stream().collect(Collectors.groupingBy(empEmployeeInfoDto -> {
            if (StringUtils.isEmpty(empEmployeeInfoDto.getOrgId())) {
                return "-1";
            }
            return empEmployeeInfoDto.getOrgId();
        }));

        //用户与员工比对是否同步
        Map<Long, Object> repetitiveUser = judgeSynchronize(sysUsers,dto.getHrmEmps());
        //顶层组织
        List<HrmOrgDto> topList = new ArrayList<>();
        hrmOrgOrgParentIdMap.forEach((key, value) -> {
            if (StringUtils.isEmpty(key)) {
                topList.addAll(value);
            }
        });
        HrmOrgDto empty = new HrmOrgDto();
        empty.setOrgId("-1");
        empty.setOrgName("无科室");
        topList.add(empty);
        //结果
        List<Transfer> res = new ArrayList<>();
        topList.forEach(item -> {
            //设置穿梭框格式
            Transfer transfer = new Transfer();
            setTransfer(item, hrmEmpOrgIdMap,hrmOrgOrgParentIdMap,transfer,repetitiveUser);
            res.add(transfer);

        });
        return res;
    }

    @Override
    public List<SysUser> queryUserByIds(List<Long> ids) {
        UserDto userDto = new UserDto();
        userDto.setUserIds(ids);
        return userReadMapper.queryUserList(userDto);
    }

    @Override
    public void modifyDept(UserDto dto, HttpServletRequest request) {
        String token = request.getHeader(MedConst.TOKEN_HEADER);
        if (StringUtils.isNotEmpty(token) && StringUtils.isNotEmpty(dto.getDept())) {
            String realToken = token.replace(MedConst.BEARER, "");
            if (dto.getSysUser() != null && dto.getSysUser().getHrmUser() != null) {
                HrmUser hrmUser = dto.getSysUser().getHrmUser();
                String ajtOrgIds = hrmUser.getAjtOrgIds();
                String hrmOrgId = hrmUser.getHrmOrgId();
                if (!ajtOrgIds.contains(dto.getDept()) && !hrmOrgId.contains(dto.getDept())) {
                    throw new AppException("非法操作");
                }
                ajtOrgIds = ajtOrgIds.replace(dto.getDept(), hrmOrgId);
                hrmOrgId = dto.getDept();
                hrmUser.setAjtOrgIds(ajtOrgIds);
                hrmUser.setHrmOrgId(hrmOrgId);
                try {
                    // 重新获取映射的科室
                    String oriOrg = userReadMapper.queryDeptMapping(hrmUser);
                    if (StringUtils.isNotEmpty(oriOrg)){
                        hrmUser.setOrlOrgIds(Arrays.asList(oriOrg.split(",")));
                    }else {
                        hrmUser.setOrlOrgIds(new ArrayList<>());
                    }
                }catch (Exception e){
                    log.error("获取科室对照失败");
                    log.error(e.toString());
                }
                Object o = RedisUtil.get(realToken);
                if(o != null){
                    HashMap hashMap = JSONObject.parseObject(JSONObject.toJSONString(RedisUtil.get(realToken)), HashMap.class);
                    hashMap.put("sysUser",dto.getSysUser());
                    RedisUtil.set(realToken, hashMap);
                }
            }
        }
    }


    /**
     * 用户与员工比对是否同步
     * @param sysUsers
     */
    private Map<Long,Object> judgeSynchronize(List<SysUser> sysUsers, List<EmpEmployeeInfoDto> hrmEmps) {

        //根据标号和姓名分组
        Map<String, List<EmpEmployeeInfoDto>> empInfoMap = hrmEmps.stream().collect(Collectors.groupingBy((emp) -> emp.getEmpCode() + emp.getEmpName()));
        Map<String, List<SysUser>> userEmpMap = sysUsers.stream().collect(Collectors.groupingBy((user) -> user.getUname() + user.getNickname()));
        Map<Long,Object> repetitiveUser = new HashMap<>();
        userEmpMap.forEach((key,value) -> {
            empInfoMap.forEach((k,v) -> {
                if (key.equals(k)) {
                    repetitiveUser.put(v.get(0).getId(),1);
                }
            });
        });
        return repetitiveUser;
    }

    /**
     *
     * @param org 组织对象
     * @param empMap user通过org_id 分组过后的 map
     * @param orgMap org 通过 orgParentId 分组
     * @param transfer  transfer
     * @param repetitiveUser 已同步的用户
     */
    private void setTransfer(HrmOrgDto org, Map<String, List<EmpEmployeeInfoDto>> empMap,Map<String, List<HrmOrgDto>> orgMap,Transfer transfer,Map<Long, Object> repetitiveUser) {
        //组织
        transfer.setLabel(org.getOrgName());
        transfer.setValue(org.getOrgId()+org.getOrgName());
        transfer.setDisabled(false);
        List<Transfer> child = new ArrayList();
        //用户
        if (empMap.get(org.getOrgId()) != null && empMap.get(org.getOrgId()).size() > 0) {
            empMap.get(org.getOrgId()).forEach(emp -> {
                Transfer t = new Transfer();
                t.setLabel(emp.getEmpName() + "  " + emp.getEmpCode());
                t.setValue(emp.getId().toString());
                t.setDisabled(repetitiveUser.get(emp.getId()) != null);
                t.setUser(true);
                child.add(t);
            });
        }
        if (orgMap.get(org.getOrgId()) !=null && orgMap.get(org.getOrgId()).size() > 0) {
            orgMap.get(org.getOrgId()).forEach(o -> {
                Transfer t = new Transfer();
                setTransfer(o, empMap,orgMap,t,repetitiveUser);
                child.add(t);
            });
        }
        if (child.size() > 0) {
            transfer.setChildren(child);
        }
    }

    /**
     * 生成树结构
     *
     * @param topMenus 顶层菜单
     * @param menus    所有菜单
     * @param dto      参数
     */
    private void generateMenuTree(List<SysMenu> topMenus,
                                  List<SysMenu> menus,
                                  UserDto dto) {
        topMenus.forEach(topMenu -> {
            List<SysMenu> children = new ArrayList<>();
            topMenu.setKey(topMenu.getId() + "");
            menus.forEach(menu -> {
                // 生成子集，分两种情况，type为1时代表查询路由，type为2时代表菜单管理页面查询
                if (MedConst.TYPE_1.equals(dto.getType()) && MedConst.TYPE_2.equals(menu.getMenuType()) && !dto.isSkip()) {
                    return;
                }
                if (topMenu.getId().equals(menu.getParentId())) {
                    children.add(menu);
                }
            });
            if (!children.isEmpty()) {
                topMenu.setChildren(children);
                generateMenuTree(children, menus, dto);
            }
        });
    }

    @Override
    public SysUser queryCacheUserInfo(UserDto dto) {
        return dto.getSysUser();
    }
}
