package com.jp.med.core.modules.sys.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.core.modules.sys.mapper.read.SysLogReadMapper;
import com.jp.med.core.modules.sys.dto.SysLogDto;
import com.jp.med.core.modules.sys.vo.SysLogVo;
import com.jp.med.core.modules.sys.service.read.SysLogReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class SysLogReadServiceImpl extends ServiceImpl<SysLogReadMapper, SysLogDto> implements SysLogReadService {

    @Autowired
    private SysLogReadMapper sysLogReadMapper;

    @Override
    public List<SysLogVo> queryList(SysLogDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return sysLogReadMapper.queryList(dto);
    }

}
