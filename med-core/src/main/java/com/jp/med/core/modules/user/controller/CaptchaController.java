package com.jp.med.core.modules.user.controller;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import com.jp.med.common.util.RedisUtil;
import com.wf.captcha.*;
import com.wf.captcha.base.Captcha;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Random;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/5 09:17
 * @description: 验证码
 */
@Controller
@RequestMapping("/captcha")
public class CaptchaController {

    @ApiOperation("生成验证码")
    @GetMapping("/generate")
    public void generate(HttpServletResponse response) throws IOException, FontFormatException {
        captcha2(response);
    }

    private void captcha1(HttpServletResponse response) throws IOException {
        // 定义图形验证码的长和宽
        LineCaptcha lineCaptcha = CaptchaUtil.createLineCaptcha(150, 80, 4, 30);
        // 存储到redis后续校验
        RedisUtil.set(lineCaptcha.getCode().toUpperCase(), lineCaptcha.getCode(),1);
        // 图形验证码写出，可以写出到文件，也可以写出到流
        lineCaptcha.write(response.getOutputStream());
        // 关闭流
        response.getOutputStream().close();
    }

    private void captcha2(HttpServletResponse response) throws IOException, FontFormatException {
        Random random = new Random();
//        int i = random.nextInt(2) + 1;
        int i = 2;
        String captchaTxt = "";
        switch (i) {
//            case 1:
//                //png图
//                SpecCaptcha captcha = new SpecCaptcha(130, 40);
//                captcha.setLen(4);
//                captcha.out(response.getOutputStream());
//                captchaTxt = captcha.text();
//                break;
//            case 2:
//                //gif动图
//                GifCaptcha gifCaptcha = new GifCaptcha(130, 40);
//                //设置纯大写字母验证码TYPE_ONLY_UPPER
//                gifCaptcha.setCharType(Captcha.TYPE_ONLY_UPPER);
//                //设置字体
//                gifCaptcha.setFont(Captcha.FONT_5);
//                gifCaptcha.out(response.getOutputStream());
//                captchaTxt = gifCaptcha.text();
//                break;
//            case 3:
//                //中文类型
//                ChineseCaptcha chineseCaptcha = new ChineseCaptcha(130, 40);
//                chineseCaptcha.out(response.getOutputStream());
//                captchaTxt = chineseCaptcha.text();
//                break;
//            case 4:
//                //中文类型gif动图
//                ChineseGifCaptcha chineseGifCaptcha = new ChineseGifCaptcha(130, 40);
//                chineseGifCaptcha.out(response.getOutputStream());
//                captchaTxt = chineseGifCaptcha.text();
//                break;
            case 2:
                //算术类型
                ArithmeticCaptcha arithmeticCaptcha = new ArithmeticCaptcha(180, 40);
                //设置几位数的运算，几个数计算
                arithmeticCaptcha.setLen(2);
                arithmeticCaptcha.out(response.getOutputStream());
                captchaTxt = arithmeticCaptcha.text();
            default:
                captcha1(response);
        }
        if (StringUtils.isNotEmpty(captchaTxt)) {
            // 存储到redis后续校验
            RedisUtil.set(captchaTxt.toUpperCase(), captchaTxt,1);
        }
    }
}
