package com.jp.med.core.modules.sys.mapper.write;

import com.jp.med.core.modules.sys.dto.SysDataRoleDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.core.modules.sys.entity.SysDataRoleMenuEntity;
import com.jp.med.core.modules.user.dto.UserDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 系统数据角色
 * <AUTHOR>
 * @email -
 * @date 2023-04-23 18:57:37
 */
@Mapper
public interface SysDataRoleWriteMapper extends BaseMapper<SysDataRoleDto> {

    /**
     * 保存数据角色菜单
     * @param entity
     */
    void saveDataRoleMenu(SysDataRoleMenuEntity entity);

    /**
     * 删除数据角色菜单
     * @param entity
     */
    void deleteDataRoleMenu(SysDataRoleMenuEntity entity);

    /**
     * 新增用户数据角色
     * @param dto
     */
    void saveUserDataRole(UserDto dto);

    /**
     * 删除用户数据角色
     * @param dto
     */
    void deleteUserDataRole(UserDto dto);

    /**
     * 通过数据角色id删除用户角色
     * @param dto
     */
    void deleteUserDataRoleByRoleId(SysDataRoleDto dto);

    /**
     * 批量新增用户数据角色
     * @param userDtos
     */
    void batchSaveDataRole(List<UserDto> userDtos);
}
