package com.jp.med.core.modules.sys.mapper.write;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.core.modules.sys.dto.SysUserFavoriteMenuDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户常用菜单写入Mapper
 * <AUTHOR>
 * @email -
 * @date 2025-01-20
 */
@Mapper
public interface SysUserFavoriteMenuWriteMapper extends BaseMapper<SysUserFavoriteMenuDto> {

    /**
     * 批量插入常用菜单
     * @param menuList 菜单列表
     */
    void batchInsert(@Param("list") List<SysUserFavoriteMenuDto> menuList);

    /**
     * 删除用户指定路径的常用菜单
     * @param username 用户名
     * @param menuPath 菜单路径
     */
    void deleteByUserAndPath(@Param("username") String username, @Param("menuPath") String menuPath);

    /**
     * 清空用户所有常用菜单
     * @param username 用户名
     */
    void clearUserFavoriteMenus(@Param("username") String username);

    /**
     * 更新菜单排序
     * @param id 菜单ID
     * @param sortOrder 排序号
     */
    void updateSortOrder(@Param("id") Long id, @Param("sortOrder") Integer sortOrder);

    /**
     * 设置PIN状态
     * @param id 菜单ID
     * @param isPinned PIN状态
     * @param pinOrder PIN排序号
     */
    void updatePinStatus(@Param("id") Long id, @Param("isPinned") String isPinned, @Param("pinOrder") Integer pinOrder);

    /**
     * 取消用户所有PIN
     * @param username 用户名
     */
    void clearAllPins(@Param("username") String username);

    /**
     * 批量更新PIN排序
     * @param pinList PIN列表
     */
    void batchUpdatePinOrder(@Param("list") List<SysUserFavoriteMenuDto> pinList);
}
