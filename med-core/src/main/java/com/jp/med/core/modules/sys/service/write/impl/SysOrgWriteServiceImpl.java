package com.jp.med.core.modules.sys.service.write.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.core.modules.sys.dto.SysOrgDto;
import com.jp.med.core.modules.sys.mapper.read.SysOrgReadMapper;
import com.jp.med.core.modules.sys.mapper.write.SysOrgWriteMapper;
import com.jp.med.core.modules.sys.service.write.SysOrgWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Transactional(readOnly = false)
@Service
public class SysOrgWriteServiceImpl extends ServiceImpl<SysOrgWriteMapper, SysOrgDto> implements SysOrgWriteService {

    @Autowired
    private SysOrgWriteMapper sysOrgWriteMapper;

    @Autowired
    private SysOrgReadMapper sysOrgReadMapper;

    @Override
    public void insertSysOrg(SysOrgDto dto) {
        sysOrgWriteMapper.insertSysOrg(dto);
    }

    @Override
    public void updateSysOrg(SysOrgDto dto) {
        sysOrgWriteMapper.updateSysOrg(dto);
    }

    @Override
    public void deleteSysOrg(SysOrgDto dto) {
        // 先删除子集
        sysOrgWriteMapper.deleteSysOrgChildren(dto);
        sysOrgWriteMapper.deleteSysOrg(dto);
    }

    @Override
    public void uploadData( SysOrgDto dto) {
//        SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH,false);
//        MultipartFile file = dto.getFile();
//        try {
//            List<SysOrgDto> sysOrgDtos = EasyPoiUtil.importExcel(file, SysOrgDto.class);
//            List<SysOrgEntity> orgEntities = sysOrgReadMapper.queryOrg(dto);
//            Map<String, List<SysOrgEntity>> collect = orgEntities.stream().collect(Collectors.groupingBy(SysOrgEntity::getOrgId));
//            SysOrgWriteMapper mapper = sqlSession.getMapper(SysOrgWriteMapper.class);
//            int count = sysOrgDtos.size();
//            int all = 0;
//            for (int i = 0; i < sysOrgDtos.size(); i++) {
//                SysOrgDto sysOrgDto = sysOrgDtos.get(i);
//                if (StringUtils.isEmpty(sysOrgDto.getOrgParentId())){
//                    sysOrgDto.setOrgParentId(dto.getHospitalId());
//                }
//                sysOrgDto.setHospitalId(dto.getHospitalId());
//                List<SysOrgEntity> sysOrgEntities = collect.get(sysOrgDto.getOrgId());
//                if (CollectionUtils.isEmpty(sysOrgEntities) || sysOrgEntities.size() == 0){
//                    all++;
//                    mapper.insertSysOrg(sysOrgDto);
//                    if (i % 1000 == 0 || all == count){
//                        sqlSession.commit();
//                        sqlSession.clearCache();
//                    }
//                }else {
//                    count--;
//                }
//            }
//        }catch (Exception e){
//            sqlSession.rollback();
//            sqlSession.close();
//            e.printStackTrace();
//        }finally {
//            sqlSession.close();
//        }
    }

    @Override
    public void graphModelDataSave(SysOrgDto dto) {
        if (CollectionUtils.isNotEmpty(dto.getDeleteList()) && CollectionUtils.isNotEmpty(dto.getAddList())) {
            dto.getDeleteList().forEach(d -> d.setHospitalId(dto.getHospitalId()));
            dto.getAddList().forEach(d -> d.setHospitalId(dto.getHospitalId()));
            BatchUtil.batch("deleteSysOrg",dto.getDeleteList(), SysOrgWriteMapper.class);
            BatchUtil.batch("insertSysOrg",dto.getAddList(), SysOrgWriteMapper.class);
        }
    }

    @Override
    public void synHrmOrg(SysOrgDto dto) {
        List<SysOrgDto> dtos = new ArrayList<>();
        for (String item : dto.getOrgInfo()) {
            String[] split = item.split(",");
            SysOrgDto sysOrgDto = new SysOrgDto();
            sysOrgDto.setOrgId(split[0]);
            sysOrgDto.setOrgParentId(split[1]);
            sysOrgDto.setOrgName(split[2]);
            sysOrgDto.setHospitalId(dto.getHospitalId());
            dtos.add(sysOrgDto);
        }
        saveBatch(dtos);
    }
}
