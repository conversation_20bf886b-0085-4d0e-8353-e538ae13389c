package com.jp.med.core.modules.sys.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.core.modules.sys.mapper.write.SysPrcsDetlWriteMapper;
import com.jp.med.common.dto.prcs.PrcsDetlDto;
import com.jp.med.core.modules.sys.service.write.SysPrcsDetlWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 流程详情
 * <AUTHOR>
 * @email -
 * @date 2023-12-13 16:21:53
 */
@Service
@Transactional(readOnly = false)
public class SysPrcsDetlWriteServiceImpl extends ServiceImpl<SysPrcsDetlWriteMapper, PrcsDetlDto> implements SysPrcsDetlWriteService {
}
