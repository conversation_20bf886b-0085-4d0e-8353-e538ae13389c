package com.jp.med.core.modules.sys.service.write.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.entity.user.SysUser;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.DateUtil;
import com.jp.med.core.modules.sys.dto.SysCollRoleDto;
import com.jp.med.core.modules.sys.mapper.read.SysCollRoleReadMapper;
import com.jp.med.core.modules.sys.mapper.write.SysCollRoleWriteMapper;
import com.jp.med.core.modules.sys.mapper.write.SysDataRoleWriteMapper;
import com.jp.med.core.modules.sys.mapper.write.SysRoleWriteMapper;
import com.jp.med.core.modules.sys.service.write.SysCollRoleWriteService;
import com.jp.med.core.modules.sys.vo.SysCollRoleVo;
import com.jp.med.core.modules.user.dto.UserDto;
import com.jp.med.core.modules.user.mapper.read.UserReadMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色归集
 * <AUTHOR>
 * @email -
 * @date 2023-12-05 17:11:17
 */
@Service
@Transactional(readOnly = false)
public class SysCollRoleWriteServiceImpl extends ServiceImpl<SysCollRoleWriteMapper, SysCollRoleDto> implements SysCollRoleWriteService {

    @Autowired
    private SysCollRoleReadMapper sysCollRoleReadMapper;

    @Autowired
    private UserReadMapper userReadMapper;

    @Autowired
    private SysCollRoleWriteMapper sysCollRoleWriteMapper;

    @Override
    public void saveCollRole(SysCollRoleDto dto) {
        dto.setCreateTime(DateUtil.getCurrentTime(null));
        dto.setCrter(dto.getSysUser().getUsername());
        sysCollRoleWriteMapper.insert(dto);
        updateDetail(dto, false);
    }

    @Override
    public void removeCollRole(SysCollRoleDto dto) {
        sysCollRoleWriteMapper.deleteById(dto);
        sysCollRoleWriteMapper.deleteCollRoleDetail(dto);
        sysCollRoleWriteMapper.deleteCollDataRoleDetail(dto);
    }

    @Override
    public void updateCollRole(SysCollRoleDto dto) {
        if (dto.getId() != null) {
            // 查询所有和归集角色相关联的用户
            UserDto userDto = new UserDto();
            userDto.setCollRoleIdsStr(dto.getId().toString());
            List<SysCollRoleVo> sysCollRoleVos = sysCollRoleReadMapper.queryList(new SysCollRoleDto());
            Map<Integer, String> menuMap = sysCollRoleVos.stream().collect(Collectors.toMap(SysCollRoleVo::getId, SysCollRoleVo::getMenuRoleIds));
            Map<Integer, String> dataMap = sysCollRoleVos.stream().collect(Collectors.toMap(SysCollRoleVo::getId, SysCollRoleVo::getDataRoleIds));
            List<SysUser> sysUsers = userReadMapper.queryUserList(userDto);
            if (CollectionUtil.isNotEmpty(sysUsers)) {
                List<UserDto> btos = new ArrayList<>();
                for (SysUser sysUser : sysUsers) {
                    Set<Long> roleIds = new HashSet<>(); // 使用 Set 代替 List
                    Set<Long> roleDataIds = new HashSet<>(); // 使用 Set 代替 List

                    if (StringUtils.isNotEmpty(sysUser.getCollRoleIdsStr())) {
                        String[] split = sysUser.getCollRoleIdsStr().split(",");
                        for (String s : split) {
                            if (Integer.parseInt(s) != dto.getId()) {
                                String s1 = menuMap.get(Integer.parseInt(s));
                                String[] split1 = s1.split(",");
                                String s2 = dataMap.get(Integer.parseInt(s));
                                String[] split2 = s2.split(",");
                                Arrays.stream(split1).forEach(ss -> roleIds.add(Long.parseLong(ss)));
                                Arrays.stream(split2).forEach(ss -> roleDataIds.add(Long.parseLong(ss)));
                            }
                        }
                    }
                    roleIds.addAll(dto.getRoleIds());
                    roleDataIds.addAll(dto.getRoleDataIds());
                    UserDto bto = new UserDto();
                    bto.setId(sysUser.getId());
                    bto.setRoleIds(longToInt(roleIds));
                    bto.setRoleDataIds(longToInt(roleDataIds));
                    btos.add(bto);
                }
                if (CollectionUtil.isNotEmpty(btos)) {
                    // 菜单角色
                    BatchUtil.batch("deleteRole", btos, SysRoleWriteMapper.class);
                    BatchUtil.batch("saveRole", btos, SysRoleWriteMapper.class);

                    // 数据角色
                    BatchUtil.batch("deleteUserDataRole", btos, SysDataRoleWriteMapper.class);
                    BatchUtil.batch("saveUserDataRole", btos, SysDataRoleWriteMapper.class);
                }
            }
            // 添加
            sysCollRoleWriteMapper.updateById(dto);
            updateDetail(dto, true);
        }
    }


    private int[] longToInt(Set<Long> ids) {
        return ids.stream()
                .mapToInt(Long::intValue) // 转换 Long 为 int
                .toArray(); // 转换为 int[]
    }

    private void updateDetail(SysCollRoleDto dto, boolean delDetail){
        if (dto.getId() != null) {
            if (CollectionUtil.isNotEmpty(dto.getRoleIds())) {
                if(delDetail){
                    sysCollRoleWriteMapper.deleteCollRoleDetail(dto);
                }
                sysCollRoleWriteMapper.insertCollRoleDetail(dto.getRoleIds(), dto.getId());
            } else{
                throw new AppException("角色为空");
            }
            if (CollectionUtil.isNotEmpty(dto.getRoleDataIds())) {
                if(delDetail){
                    sysCollRoleWriteMapper.deleteCollDataRoleDetail(dto);
                }
                sysCollRoleWriteMapper.insertCollDataRoleDetail(dto.getRoleDataIds(), dto.getId());
            } else {
                throw new AppException("数据角色为空");
            }
        } else {
            throw new AppException("写入数据失败");
        }
    }
}
