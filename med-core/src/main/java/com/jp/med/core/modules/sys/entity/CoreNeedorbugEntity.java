package com.jp.med.core.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 需求orBug记录
 * <AUTHOR>
 * @email -
 * @date 2024-01-16 15:20:11
 */
@Data
@TableName("sys_needorbug")
public class CoreNeedorbugEntity {

	/**
	 * id
	 */
	@TableId("id")
	private Integer id;

	/**
	 * 提出需求/bug的员工id
	 */
	@TableField("emp_id")
	private Integer empId;

	/**
	 * 提出需求/bug的科室id
	 */
	@TableField("org_id")
	private String orgId;

	/**
	 * 提出人邮箱
	 */
	@TableField("email")
	private String email;

	/**
	 * 提出人电话
	 */
	@TableField("phone")
	private String phone;

	/**
	 * $column.comments
	 */
	@TableField("system_id")
	private Integer systemId;

	/**
	 * $column.comments
	 */
	@TableField("menu_id")
	private String menuId;

	// 多个菜单id
	@TableField(exist = false)
	private String[] menuIds;

	/**
	 * 需求还是bug(1:需求,2bug)
	 */
	@TableField("forward_type")
	private String forwardType;

	/**
	 * 状态更新时间
	 */
	@TableField("status_time")
	private String statusTime;

	/**
	 * 标题
	 */
	@TableField("title")
	private String title;

	/**
	 * $column.comments
	 */
	@TableField("description")
	private String description;

	/**
	 * 需求备注
	 */
	@TableField("remark")
	private String remark;

	/**
	 * 需求或bug状态(1: 等待; 2: 采纳; 3: 完成)
	 */
	@TableField("status")
	private String status;

	/**
	 * 创建时间
	 */
	@TableField("created_at")
	private String createdAt;

	/**
	 * 逻辑状态(0 未删除 1已删除)
	 */
	@TableField("active_flag")
	private Integer activeFlag;

	/**
	 * 对接工程师id
	 */
	@TableField("eng_id")
	private Integer engId;

	/**
	 * 工程师回复
	 */
	@TableField("reply")
	private String reply;

	/**
	 * 完成时间
	 */
	@TableField("finish_time")
	private String finishTime;

	/**
	 *
	 * 组织id
	 */
	@TableField("hospital_id")
	private String hospitalId;

	/**
	 * 附件
	 */
	@TableField("att")
	private String att;

}