package com.jp.med.core.modules.sys.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 对接工程师信息
 * <AUTHOR>
 * @email -
 * @date 2024-01-16 15:20:12
 */
@Data
@TableName("sys_engineer_info" )
public class CoreEngineerInfoDto extends CommonQueryDto {

    /** 工程师id */
    @TableId("id")
    private Integer id;

    /** 工程师姓名 */
    @TableField("eng_name")
    private String engName;

    /** 工程师联系电话 */
    @TableField("eng_phone")
    private String engPhone;

    /** 工程师邮箱(用于发送文件如excel) */
    @TableField("eng_email")
    private String engEmail;

    /** 工程师微信 */
    @TableField("eng_wechat")
    private String engWechat;

    /** 工程师团队 */
    @TableField("eng_team")
    private String engTeam;

    /** 备注(负责系统，工作职责，专长等) */
    @TableField("remark")
    private String remark;

    /** 工程师状态(0：在职, 1:休假) */
    @TableField("status")
    private String status;

    /** 状态 */
    @TableField("active_flag")
    private Integer activeFlag;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 修改时间 */
    @TableField("update_time")
    private String updateTime;

    /** 删除时间 */
    @TableField("delete_time")
    private String deleteTime;

    /** 组织id */
    @TableField("hospital_id")
    private String hospitalId;
}
