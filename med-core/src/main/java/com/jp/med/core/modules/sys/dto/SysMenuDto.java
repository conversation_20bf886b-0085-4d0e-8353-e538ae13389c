package com.jp.med.core.modules.sys.dto;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/6 15:16
 * @description: 系统菜单
 */
@Data
public class SysMenuDto extends CommonQueryDto {

    /** id */
    @TableId("menu_id")
    private Long id;

    /** 父级ID */
    @TableField(value = "parent_id", updateStrategy = FieldStrategy.IGNORED)
    private Long parentId;

    /** 父级菜单名称 */
    @TableField(exist = false)
    private String parentMenuName;

    /** 菜单名称 */
    @TableField("name")
    private String menuName;

    /** 菜单路径（配置的前端访问路径） */
    @TableField("url")
    private String menuUrl;

    /** 组件路径（配置的前端访问路径） */
    @TableField("comp_url")
    private String compUrl;

    /** 授权列表 */
    private String perms;

    /** 菜单类型 0：菜单 1：按钮 */
    @TableField("type")
    private String menuType;

    /** 菜单图标 */
    @TableField("icon")
    private String menuIcon;

    /** 菜单排序号 */
    @TableField(value = "order_num", updateStrategy = FieldStrategy.IGNORED)
    private String menuOrderNum;

    /** 键值 */
    @TableField(exist = false)
    private String key;

    /** 是否缓存 */
    @TableField("keep_alive")
    private String keepAlive;


    /** 系统类型(0:web系统,1:app系统) */
    @TableField("sys_type")
    private String sysType;

    /** 系统id */
    private Long systemId;
}
