package com.jp.med.core.modules.sys.controller;

import com.jp.med.common.util.SysConfigUtil;
import com.jp.med.core.config.GlobalInitConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.core.modules.sys.dto.SysConfigDto;
import com.jp.med.core.modules.sys.service.read.SysConfigReadService;
import com.jp.med.core.modules.sys.service.write.SysConfigWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.Map;


/**
 * 系统配置信息表
 *
 * <AUTHOR>
 * @email -
 * @date 2023-03-24 09:31:58
 */
@Api(value = "系统配置信息表", tags = "系统配置信息表")
@RestController
@RequestMapping("sys/config")
public class SysConfigController {
    @Autowired
    private SysConfigReadService sysConfigReadService;

    @Autowired
    private SysConfigWriteService sysConfigWriteService;

    @Autowired
    private GlobalInitConfig globalInitConfig;

    /**
     * 列表
     */
    @ApiOperation("查询系统配置信息表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody SysConfigDto dto){
        return CommonResult.paging(sysConfigReadService.queryList(dto));
    }


    /**
     * 保存
     */
    @ApiOperation("新增系统配置信息表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody SysConfigDto dto){
        sysConfigWriteService.saveConfig(dto);

        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改系统配置信息表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody  SysConfigDto dto){
        sysConfigWriteService.updateConfig(dto);

        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除系统配置信息表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody  SysConfigDto dto){
        sysConfigWriteService.removeById(dto.getId());
        return CommonResult.success();
    }

    @ApiOperation("刷新系统配置信息表")
    @PostMapping("/refresh")
    public CommonResult<?> refresh(@RequestBody SysConfigDto dto){
        globalInitConfig.sysConfig("刷新系统配置");
        return CommonResult.success();
    }

    @ApiOperation("获取系统配置信息表")
    @PostMapping("/getSysConfig")
    public CommonResult<?> getSysConfig(@RequestBody SysConfigDto dto){
        return CommonResult.success(SysConfigUtil.getSysConfig());
    }

}
