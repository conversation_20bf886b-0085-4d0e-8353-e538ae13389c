package com.jp.med.core.modules.sys.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.core.modules.sys.dto.SysDataRoleDto;
import com.jp.med.core.modules.sys.entity.SysDataRoleMenuEntity;
import com.jp.med.core.modules.sys.vo.SysDataRoleVo;

import java.util.List;

/**
 * 系统数据角色
 * <AUTHOR>
 * @email -
 * @date 2023-04-23 18:57:37
 */
public interface SysDataRoleReadService extends IService<SysDataRoleDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<SysDataRoleVo> queryList(SysDataRoleDto dto);

    /**
     * 查询数据角色菜单
     * @param dto
     * @return
     */
    List<SysDataRoleMenuEntity> queryDataRoleMenu(SysDataRoleDto dto);
}

