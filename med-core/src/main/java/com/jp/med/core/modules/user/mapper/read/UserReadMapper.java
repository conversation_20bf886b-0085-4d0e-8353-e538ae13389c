package com.jp.med.core.modules.user.mapper.read;

import com.jp.med.common.dto.common.CommonFeignDto;
import com.jp.med.common.entity.sys.SysMenu;
import com.jp.med.common.entity.sys.SysRole;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.entity.user.SysUser;
import com.jp.med.core.modules.user.dto.UserDto;
import com.jp.med.core.modules.user.entity.SysUserTokenEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/16 11:47
 * @description:
 */
public interface UserReadMapper {

    /**
     * 通过用户名查询用户信息
     * @param username
     * @return
     */
    SysUser getUserByUsername(String username);

    /**
     * 获取用户权限
     * @param id
     * @return
     */
    List<SysRole> getPermissionsByUserId(Long id);

    /**
     * 通过用户名查询用户所拥有的的菜单权限
     * @param dto
     * @return
     */
    List<SysRole> queryUserAuthByUsername(CommonFeignDto dto);

    /**
     * 查询用户菜单
     * @param dto
     * @return
     */
    List<SysMenu> queryUserMenu(UserDto dto);

    /**
     * 查询系统菜单
     * @param dto
     * @return
     */
    List<SysMenu> querySysMenu(UserDto dto);

    /**
     * 查询用户信息
     * @param dto
     * @return
     */
    List<SysUser> queryUserList(UserDto dto);

    /**
     * 根据用户名判断用户是否存在
     * @param dto
     * @return
     */
    SysUser existUser(UserDto dto);

    /**
     * 通过查询条件查询系统菜单
     * @param dto
     * @return
     */
    List<SysMenu> querySysMenuByCon(UserDto dto);

    /**
     * 查询用户token
     * @param ids
     * @return
     */
    List<SysUserTokenEntity> queryUserToken(List<String> ids);

    /**
     * 查询科室的映射
     * @param hrmUser
     * @return
     */
    String queryDeptMapping(HrmUser hrmUser);

}
