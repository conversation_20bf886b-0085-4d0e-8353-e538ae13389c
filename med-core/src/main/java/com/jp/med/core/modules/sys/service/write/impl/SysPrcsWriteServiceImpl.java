//package com.jp.med.core.modules.sys.service.write.impl;
//
//import com.alibaba.fastjson.JSON;
//import com.jp.med.common.constant.MedConst;
//import com.jp.med.common.constant.OSSConst;
//import com.jp.med.common.dto.prcs.PrcsDetlDto;
//import com.jp.med.common.exception.AppException;
//import com.jp.med.common.util.ActivitiUtil;
//import com.jp.med.common.util.BatchUtil;
//import com.jp.med.common.util.OSSUtil;
//import com.jp.med.common.util.ULIDUtil;
//import com.jp.med.core.modules.sys.mapper.write.SysPrcsDetlWriteMapper;
//import lombok.extern.slf4j.Slf4j;
//import org.activiti.bpmn.model.*;
//import org.activiti.bpmn.model.Process;
//import org.springframework.stereotype.Service;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import com.jp.med.core.modules.sys.mapper.write.SysPrcsWriteMapper;
//import com.jp.med.common.dto.prcs.PrcsDto;
//import com.jp.med.core.modules.sys.service.write.SysPrcsWriteService;
//import org.springframework.transaction.annotation.Transactional;
//
//import javax.annotation.Resource;
//import java.text.SimpleDateFormat;
//import java.util.*;
//
///**
// * 流程主表
// *
// * <AUTHOR>
// * @email -
// * @date 2023-12-13 16:02:39
// */
//@Service
//@Transactional(readOnly = false)
//@Slf4j
//public class SysPrcsWriteServiceImpl extends ServiceImpl<SysPrcsWriteMapper, PrcsDto> implements SysPrcsWriteService {
//
//
//    @Resource
//    private SysPrcsWriteMapper sysPrcsWriteMapper;
//
//    @Resource
//    private SysPrcsDetlWriteMapper sysPrcsDetlWriteMapper;
//
//    @Override
//    public boolean save(PrcsDto prcsDto) {
//        // 上传文件
//        String file = OSSUtil.uploadFile(OSSConst.BUCKET_CORE, OSSConst.CORE_PRCS_INFO_PATH, prcsDto.getFile());
//        prcsDto.setPrcsFile(file);
//        try {
//            prcsDto.setUsername(prcsDto.getSysUser().getUsername());
//            prcsDto.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
//            prcsDto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
//            String generate = ULIDUtil.generate();
//            List<PrcsDetlDto> resultList = new ArrayList<>();
//            sysPrcsWriteMapper.insert(prcsDto);
//            // 部署到activiti解析bpmn流程图
//            ActivitiUtil.deployByOSS(OSSConst.BUCKET_CORE, prcsDto.getPrcsFile(), generate);
//            List<Process> drg = ActivitiUtil.getTasksByDeployment(generate);
//            Map<String, FlowElement> flowElementMap = drg.get(0).getFlowElementMap();
//            for (Map.Entry<String, FlowElement> entry : flowElementMap.entrySet()) {
//                if (entry.getValue() instanceof UserTask) {
//                    UserTask userTask = (UserTask) entry.getValue();
//                    PrcsDetlDto prcsDetlDto = setDetail(userTask.getAssignee(), userTask.getId(), userTask.getName(), "1", prcsDto.getPrcsCode());
//                    getChildNode(userTask.getOutgoingFlows(), prcsDetlDto);
//                    resultList.add(prcsDetlDto);
//                } else if (entry.getValue() instanceof ExclusiveGateway) {
//                    ExclusiveGateway exclusiveGateway = (ExclusiveGateway) entry.getValue();
//                    PrcsDetlDto prcsDetlDto = setDetail(null, exclusiveGateway.getId(), exclusiveGateway.getName(), "2", prcsDto.getPrcsCode());
//                    getChildNode(exclusiveGateway.getOutgoingFlows(), prcsDetlDto);
//                    resultList.add(prcsDetlDto);
//                } else if (entry.getValue() instanceof SequenceFlow) {
//                    SequenceFlow sequenceFlow = (SequenceFlow) entry.getValue();
//                } else if (entry.getValue() instanceof StartEvent) {
//                    StartEvent startEvent = (StartEvent) entry.getValue();
//                    PrcsDetlDto prcsDetlDto = setDetail(null, startEvent.getId(), startEvent.getName(), "0", prcsDto.getPrcsCode());
//                    getChildNode(startEvent.getOutgoingFlows(), prcsDetlDto);
//                    resultList.add(prcsDetlDto);
//                } else if (entry.getValue() instanceof EndEvent) {
//                    EndEvent endEvent = (EndEvent) entry.getValue();
//                    PrcsDetlDto prcsDetlDto = setDetail(null, endEvent.getId(), endEvent.getName(), "3", prcsDto.getPrcsCode());
//                    resultList.add(prcsDetlDto);
//                }
//            }
//            ActivitiUtil.undeploy(generate);
//            BatchUtil.batch(resultList, SysPrcsDetlWriteMapper.class);
//            return true;
//        } catch (Exception e) {
//            log.error(e.toString());
//            OSSUtil.removeFile(OSSConst.BUCKET_CORE, prcsDto.getPrcsFile());
//            throw new AppException("添加流程失败");
//        }
//    }
//
//
//    private PrcsDetlDto setDetail(String chker, String nodeCode, String nodeName, String type, String prcsCode) {
//        PrcsDetlDto prcsDetlDto = new PrcsDetlDto();
//        prcsDetlDto.setNodeCode(nodeCode);
//        prcsDetlDto.setNodeType(type);
//        prcsDetlDto.setNodeName(nodeName);
//        prcsDetlDto.setChker(chker);
//        prcsDetlDto.setPrcsCode(prcsCode);
//        return prcsDetlDto;
//    }
//
//    private void getChildNode(List<SequenceFlow> outgoingFlows, PrcsDetlDto prcsDetlDto){
//        Map<String, String> vai = new HashMap<>();
//        for (SequenceFlow outgoingFlow : outgoingFlows) {
//            String expression = outgoingFlow.getConditionExpression();
//            String targetRef = outgoingFlow.getTargetRef();
//            if (!Objects.isNull(expression)) {
//                vai.put(expression, targetRef);
//            }else {
//                vai.put("1", targetRef);
//            }
//        }
//        if (!vai.isEmpty()) {
//            prcsDetlDto.setChildCode(JSON.toJSONString(vai));
//        }
//    }
//
//    @Override
//    public boolean removeById(PrcsDto dto) {
//        int deleteById = sysPrcsWriteMapper.deleteById(dto);
//        if (deleteById != 1){
//            throw new AppException("流程删除失败");
//        }
//        PrcsDetlDto prcsDetlDto = new PrcsDetlDto();
//        prcsDetlDto.setPrcsCode(dto.getPrcsCode());
//        int i = sysPrcsDetlWriteMapper.deleteByCode(prcsDetlDto);
//        if (i < 1){
//            throw new AppException("流程信息删除失败");
//        }
//        OSSUtil.removeFile(OSSConst.BUCKET_CORE, dto.getPrcsFile());
//        return true;
//    }
//}
