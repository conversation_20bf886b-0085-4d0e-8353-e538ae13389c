package com.jp.med.core.modules.sys.service.write.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.context.UserContext;
import com.jp.med.common.util.DateUtil;
import com.jp.med.core.modules.sys.dto.SysUserFavoriteMenuDto;
import com.jp.med.core.modules.sys.mapper.write.SysUserFavoriteMenuWriteMapper;
import com.jp.med.core.modules.sys.service.write.SysUserFavoriteMenuWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 用户常用菜单写入服务实现
 * <AUTHOR>
 * @email -
 * @date 2025-01-20
 */
@Service
@Transactional(readOnly = false)
public class SysUserFavoriteMenuWriteServiceImpl 
    extends ServiceImpl<SysUserFavoriteMenuWriteMapper, SysUserFavoriteMenuDto> 
    implements SysUserFavoriteMenuWriteService {

    @Autowired
    private SysUserFavoriteMenuWriteMapper sysUserFavoriteMenuWriteMapper;

    @Override
    public void addFavoriteMenu(SysUserFavoriteMenuDto dto) {
        String username = getCurrentUsername();
        
        // 设置基本信息
        dto.setUsername(username);
        dto.setCreateTime(DateUtil.getCurrentTime(null));
        dto.setUpdateTime(DateUtil.getCurrentTime(null));
        dto.setStatus("1"); // 默认启用
        
        // 如果没有设置排序，设置为最大值+1
        if (dto.getSortOrder() == null) {
            // 这里可以查询当前用户的最大排序号，然后+1
            dto.setSortOrder(99);
        }
        
        // 保存到数据库
        this.save(dto);
    }

    @Override
    public void removeFavoriteMenu(SysUserFavoriteMenuDto dto) {
        String username = getCurrentUsername();
        
        if (StrUtil.isNotBlank(dto.getMenuPath())) {
            // 根据用户名和菜单路径删除
            sysUserFavoriteMenuWriteMapper.deleteByUserAndPath(username, dto.getMenuPath());
        } else if (dto.getId() != null) {
            // 根据ID删除
            this.removeById(dto.getId());
        }
    }

    @Override
    public void updateFavoriteMenuSort(SysUserFavoriteMenuDto dto) {
        if (dto.getId() != null && dto.getSortOrder() != null) {
            dto.setUpdateTime(DateUtil.getCurrentTime(null));
            sysUserFavoriteMenuWriteMapper.updateSortOrder(dto.getId(), dto.getSortOrder());
        }
    }

    @Override
    public void batchSaveFavoriteMenus(List<SysUserFavoriteMenuDto> menuList) {
        String username = getCurrentUsername();
        String currentTime = DateUtil.getCurrentTime(null);
        
        // 设置公共字段
        for (SysUserFavoriteMenuDto menu : menuList) {
            menu.setUsername(username);
            menu.setCreateTime(currentTime);
            menu.setUpdateTime(currentTime);
            menu.setStatus("1");
        }
        
        // 批量插入
        sysUserFavoriteMenuWriteMapper.batchInsert(menuList);
    }

    @Override
    public void clearUserFavoriteMenus(String username) {
        if (StrUtil.isBlank(username)) {
            username = getCurrentUsername();
        }
        sysUserFavoriteMenuWriteMapper.clearUserFavoriteMenus(username);
    }

    @Override
    public void togglePin(SysUserFavoriteMenuDto dto) {
        String username = getCurrentUsername();

        // 检查是否已存在该菜单的收藏记录
        // 如果不存在，先添加到收藏
        if (dto.getId() == null && StrUtil.isNotBlank(dto.getMenuPath())) {
            // 先检查是否已收藏
            // 这里可以调用查询方法检查
            addFavoriteMenu(dto);
        }

        // 检查当前PIN数量限制（最多4个）
        if ("1".equals(dto.getIsPinned())) {
            // 要设置PIN，检查数量限制
            // 这里可以添加数量检查逻辑
        }

        // 更新PIN状态
        if (dto.getId() != null) {
            sysUserFavoriteMenuWriteMapper.updatePinStatus(
                dto.getId(),
                dto.getIsPinned(),
                dto.getPinOrder()
            );
        }
    }

    @Override
    public void updatePinOrder(SysUserFavoriteMenuDto dto) {
        if (dto.getMenuList() != null && !dto.getMenuList().isEmpty()) {
            // 批量更新PIN排序
            sysUserFavoriteMenuWriteMapper.batchUpdatePinOrder(dto.getMenuList());
        }
    }

    @Override
    public void clearAllPins(String username) {
        if (StrUtil.isBlank(username)) {
            username = getCurrentUsername();
        }
        sysUserFavoriteMenuWriteMapper.clearAllPins(username);
    }

    /**
     * 获取当前用户名
     * @return 用户名
     */
    private String getCurrentUsername() {
        try {
            return UserContext.getEmpCode();
        } catch (Exception e) {
            throw new RuntimeException("获取用户上下文失败: " + e.getMessage(), e);
        }
    }
}
